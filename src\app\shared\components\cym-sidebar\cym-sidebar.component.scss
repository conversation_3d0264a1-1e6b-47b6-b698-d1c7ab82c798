@import '~styles/app';

#results-div {
    height: 100vh;
    z-index: 0;
    padding: 0;
}

.reportsContainer {
    overflow-x: hidden;
}

.sidebarContainer {
    overflow: hidden !important;
    height: 100%;
    @include get-theme-content-component();
    padding:0 15px;
    background: transparent !important;
}

.sidebar-div {
    padding: 10px;
    border-radius: 0;
    border: 0;
    @include get-theme-sidebar-component();
}

.fullHeight {
    height: 100%;
}

.paint-icons {
    color: $fontColor;
}

.checkbox-align {
    background-color: transparent;
    float: right;
}

.checkbox-align-rtl {
    background-color: transparent;
    margin-left: 0 !important;
    float: left !important;
    margin-right: $checkbox-align-rtl-margin;
}

.sidebar-header {
    height: 50px;
    line-height: 15px;
}
.main-sidebar {
    .sidebar-toggler {
        display: block;
    }    
}
.sidebar-toggler {
    @include get-theme-sidebar-toggler();
    display: none;
    position: absolute;
    bottom: 20px;
    right: -13px;
    width: 26px;
    height: 26px;
    border-radius: 13px;
    z-index: 1;
    cursor: pointer;
    :host-context(.rtl) &{
        left: -13px;
        right: auto;
    }
}
.toggle-btn-arrow {
    position: absolute;
    top: 2px;
    left: 8px;
    :host-context(.rtl) &{
        left: 6px;
    }
    &.open {
        left: 6px;
        :host-context(.rtl) &{
            left: 8px;
        }
    }
    :host-context(.theme-dark) &{
        color: $theme-dark-font-color;
    }

    :host-context(.theme-light) &{
        color: $theme-light-font-color;
    }
    
}
.navbar-brand,
.navbar-brand:focus,
.navbar-brand:hover {
    color: $sidebar-color; 
    outline: none !important;
}

button:focus {
    text-decoration: none;
    outline: none;
    border: none;
    box-shadow: none;
}
