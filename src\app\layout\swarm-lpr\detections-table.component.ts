import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { CymsidebarService } from 'app/shared/components/cym-sidebar/cym-sidebar.service';
import { TablePaginatorEvent } from 'app/shared/components/ng-turbo-table/models/table-paginator-event.interface';
import { ElementTableCell, TableActionsComponent, TableCell, TableColumnProperties } from 'app/shared/components/ng-turbo-table/models/table.models';

import { Guid } from 'app/shared/enum/guid';
import { DownloadStatus } from 'app/shared/modules/data-layer/models/detections/download-status';
import * as moment from 'moment';
import { Subscription } from 'rxjs';
import { DetectionPageOptions } from '../../shared/dtos/detection-page-options';


import { ActivatedRoute, Router } from '@angular/router';
import { DetectionService } from 'app/services/detecions/detection.service';
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';
import { NgTurboTableComponent } from 'app/shared/components/ng-turbo-table/ng-turbo-table.component';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { NotificationCategory } from 'app/shared/modules/data-layer/enum/reports/notification-category.enum';
import { Detection } from 'app/shared/modules/data-layer/models/detections/detection';
import { NotificationMessageDTO } from 'app/shared/modules/data-layer/services/data-chunks/notification-message.dto';
import { SignalRService } from 'app/shared/services/signalR.service';
import { ISignalRConnection } from 'ng2-signalr';
import { MessageService } from 'primeng/api';


@Component({
  selector: 'detections-table',
  templateUrl: './detections-table.component.html',
  styleUrls: ['./detections-table.component.scss'],
  providers: [CymsidebarService],

})//TODO is thistype a duplicate?
export class DetectionsTableComponent implements OnInit, OnDestroy, OnChanges {

  @ViewChild('platePhotoTemplate', {static: true}) platePhotoTemplate: TemplateRef<TableActionsComponent>;
  @ViewChild('actionsTemplate', {static: true}) actionsTemplate: TemplateRef<TableActionsComponent>;
  @ViewChild('photosModal', {static:false}) photosModal: AppModal;
  @ViewChild("turboTable", {static:false}) turboTable: NgTurboTableComponent;

  showTableCaption: boolean = true;

  columns: TableColumnProperties[] = [];
  data: { [columnField: string]: TableCell | string }[] = [];
  private subscriptions: Subscription[] = [];
  pageSize: number = 10;
  totalRecords: number = 0;
  pageIndex: number = 0;
  plateValue : string = "";
  showCheckboxes : boolean = false;
  currentRecord: { [columnField: string]: TableCell | string };
  actionText: string;
  private percent: string = " %";
  private latestSubscriptionValue : string = '';
  private ngSignalRConnection: ISignalRConnection;

  constructor(
    private detectionService: DetectionService,
    private translateService: TranslateService,
    private messageService: MessageService,
    private router: Router,
    private route: ActivatedRoute,
    private signalRService: SignalRService,
    ) {

    }

  ngOnInit(): void {

    this.generateColumns();

    this.actionText = this.translateService.instant("detectionList.viewOnMap");

    let notificationSubscription = this.signalRService.GetConnection('NotificationHub').subscribe(connection => {
          this.ngSignalRConnection = connection;

          connection.listenFor<string>('onNotify').subscribe((message:string) => {
            if (!this.plateValue || this.plateValue.length === 0)
            {
              return;
            }

            let parsedMessage = JSON.parse(message);
            let notificationMessage = new NotificationMessageDTO(parsedMessage).toModel();

            if(notificationMessage.header.type !== NotificationCategory.SwarmLPRNotification) {
                return;
            }

            let detection = this.detectionService.ConvertToDetection(parsedMessage.Payload);
            if (detection.plateNo.indexOf(this.plateValue) < 0)
            {
              return;
            }

            let alreadyExisting = this.data.filter(currentDetection => currentDetection.id === detection.id);
            if (alreadyExisting.length > 0)
            {
              return;
            }

            let record = this.ConvertToRecord(detection);
            let records = this.data;
            records.unshift(record);
            this.totalRecords++;
            this.data = records;

            this.turboTable.appendToSelection(record);
          });
        }, error => {
            console.error(error);
        });
      this.subscriptions.push(notificationSubscription);

    let activeRouteSubscription = this.route.queryParams.subscribe((params) => {
        if(params['search']){
          this.plateValue = params['search'];
        }

        if (params['pageIndex'])
        {
          this.pageIndex = parseInt(params['pageIndex']);

        }

        this.getDetections();
      });
    this.subscriptions.push(activeRouteSubscription);
  }

  ngOnChanges(changes: SimpleChanges): void{


  }

  onTurboTablePaginatorEvent(event: TablePaginatorEvent): void {

    this.pageIndex = event.first / this.pageSize;

    this.getDetections();
  }

  onSearchEvent(value: string)
  {
    this.plateValue = value;

    if (this.plateValue.length == 0 || this.plateValue.length > 1)
    {
      this.getDetections();
    }
  }

  onActionEvent(plateValue: string){

    this.router.navigate(['vehicleTraffic'],
      { queryParams: { 'plateNumber': plateValue, 'itemId': null,
      'search': this.plateValue, 'pageIndex': this.pageIndex }});
  }

  onModalConfirm(){
    this.photosModal.closeModal();
    this.currentRecord = null;
  }

  onPlatePhotoClick(record)
  {
    this.currentRecord = record;
    this.photosModal.openModal();
  }

  getDetections(): void{
    let subscribeForNotifications: boolean = (this.plateValue && this.plateValue.length > 0) ? true : false;
    let plateValue = this.plateValue;

    let pageOptions: DetectionPageOptions = new DetectionPageOptions({
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      plateValue: plateValue,
      isPlateValueExactMatch: false,
      subscribeForNotifications: subscribeForNotifications,
      unsubscribePlateValue: this.latestSubscriptionValue
    });

    this.detectionService.getDetectionsPage(pageOptions).subscribe((detectionsResponse) => {

      this.turboTable.setPageIndex(this.pageIndex);
      this.turboTable.setSearchValue(this.plateValue);

      this.latestSubscriptionValue = subscribeForNotifications ? plateValue : '';

        let records : { [columnField: string]: TableCell | string }[] = [];
        detectionsResponse.Items.forEach((item) => {
          let record = this.ConvertToRecord(item);
          records.push(record);
        });

        this.data = records;
        this.totalRecords = detectionsResponse.TotalCount;
      });
  }

  downloadPhotos(item: { [columnField: string]: TableCell | string }) :void{

    let itemId : string = item.id.toString();
    let laneId: Guid = Guid.parse( item.laneId.toString());

    this.detectionService.downloadPhotos(itemId, laneId).subscribe(downloadStatuses => {
      item.plateImageDownloaded = (downloadStatuses.plateStatus == DownloadStatus.Ok).toString();
      item.detectionImageDownloaded = (downloadStatuses.detectionStatus == DownloadStatus.Ok).toString();

      this.detectionService.checkForErrors(downloadStatuses);
    }, error =>{
        this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error),
          detail: this.translateService.instant("detectionList.downloadFailed")});
    });
  }

  openVehicleTraffic(item: { [columnField: string]: TableCell | string }):void{

    let plateNumber : string = item.plateNo.toString();
    let itemId : string = item.id.toString();

    this.router.navigate(['/vehicleTraffic'],
      { queryParams: { 'plateNumber': plateNumber, 'itemId': itemId,
      'search': this.plateValue, 'pageIndex': this.pageIndex }});
  }

  generateColumns(): void {

    let laneColumn = new TableColumnProperties({
      field: "cameraName",
      header: this.translateService.instant("detectionList.camera"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,

    })
    this.columns.push(laneColumn);

    let vehicleBrandColumn = new TableColumnProperties({
      field: "vehicleBrand",
      header: this.translateService.instant("detectionList.brand"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(vehicleBrandColumn);

    let vehicleColorColumn = new TableColumnProperties({
      field: "vehicleColor",
      header: this.translateService.instant("detectionList.color"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(vehicleColorColumn);

    let plateCountryColumn = new TableColumnProperties({
      field: "plateCountry",
      header: this.translateService.instant("detectionList.country"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(plateCountryColumn);

    let plateNumberColumn = new TableColumnProperties({
      field: "plateNo",
      header: this.translateService.instant("detectionList.licensePlate"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(plateNumberColumn);

    let vehicleModelColumn = new TableColumnProperties({
      field: "vehicleModel",
      header: this.translateService.instant("detectionList.vehicleModel"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(vehicleModelColumn);

    let vehicleTypeColumn = new TableColumnProperties({
      field: "vehicleType",
      header: this.translateService.instant("detectionList.vehicleType"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(vehicleTypeColumn);

    let cameraTimeColumn = new TableColumnProperties({
      field: "cameraTime",
      header: this.translateService.instant("detectionList.cameraTime"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(cameraTimeColumn);

    let plateConfidenceColumn = new TableColumnProperties({
      field: "plateConfidence",
      header: this.translateService.instant("detectionList.plateConfidence"),
      visibile: false,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(plateConfidenceColumn);

    let vehicleConfidenceColumn = new TableColumnProperties({
      field: "vehicleConfidence",
      header: this.translateService.instant("detectionList.vehicleConfidence"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(vehicleConfidenceColumn);

    let actionsColumn = new TableColumnProperties({
      field: "actions",
      header: this.translateService.instant("detectionList.actions"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(actionsColumn);

    let platePhotoColumn = new TableColumnProperties({
      field: "platePhoto",
      header: this.translateService.instant("detectionList.platePhoto"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(platePhotoColumn);
  }

  private ConvertToRecord(item: Detection): { [columnField: string]: TableCell | string }
  {
    let record : { [columnField: string]: TableCell | string } = {};
    record.id = item.id;
    record.laneId = item.laneId.toString();
    record.plateNo = item.plateNo;
    record.plateCountry = item.plateCountry;
    record.plateConfidence = (item.plateConfidence).toFixed(0) + this.percent;
    record.vehicleBrand = item.vehicleBrand;
    record.vehicleModel = item.vehicleModel;
    record.vehicleType = item.vehicleType;
    record.vehicleColor = item.vehicleColor;
    record.vehicleConfidence = item.vehicleConfidence.toFixed(0) + this.percent;
    record.cameraName = item.cameraName;
    record.plateImageDownloaded = item.plateImageDownloaded.toString();
    record.detectionImageDownloaded = item.detectionImageDownloaded.toString();
    record.platePhotoUrl = this.detectionService.calculatePhotoUrl(item.id, item.laneId, true);
    record.detectionPhotoUrl = this.detectionService.calculatePhotoUrl(item.id, item.laneId, false);
    record.viewOnMapId = "viewOnMap_" + record.id;
    record.downloadPhotosId = "downloadPhotos_" + record.id;
    record.platePhotoId = "platePhoto_" + record.id;

    record.cameraTime = item.cameraTime ? item.cameraTime.toString() : '';

    let platePhotoCell: TableCell = new ElementTableCell(this.platePhotoTemplate);
    record['platePhoto'] = platePhotoCell;

    let actionsCell: TableCell = new ElementTableCell(this.actionsTemplate);
    record['actions'] = actionsCell;

    return record;
  }

  ngOnDestroy(): void {
    if (this.latestSubscriptionValue.length > 0)
    {
      let unsubscribeSubscription = this.detectionService.UnsubscribeFromNotifications(this.latestSubscriptionValue,
        false)
        .subscribe((dummyBoolean) => {

        this.subscriptions.forEach(item => item.unsubscribe());
      });

      this.subscriptions.push(unsubscribeSubscription);
    }
    else{
      this.subscriptions.forEach(item => item.unsubscribe());
    }
  }
}
