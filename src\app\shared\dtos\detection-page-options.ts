import { PageOptions } from "app/shared/modules/data-layer/models/paging/page-options";

export interface DetectionPageOptionsConfig {
    pageIndex: number;
    pageSize: number;
    plateValue?: string;
    isPlateValueExactMatch?: boolean;
    subscribeForNotifications?: boolean;
    unsubscribePlateValue?: string;
    subscribeForAnyPlateValue?: boolean;
    startTimestamp?: Date;
    endTimestamp?: Date;
}

export class DetectionPageOptions extends PageOptions
{
    private plateValue: string;
    private isPlateValueExactMatch: boolean;
    private subscribeForNotifications: boolean;
    private unsubscribePlateValue: string;
    private subscribeForAnyPlateValue: boolean;
    private startTimestamp: Date | null;
    private endTimestamp: Date | null;

    constructor(config: DetectionPageOptionsConfig) {
        super(config.pageIndex, config.pageSize);

        this.plateValue = config.plateValue || '';
        this.isPlateValueExactMatch = config.isPlateValueExactMatch || false;
        this.subscribeForNotifications = config.subscribeForNotifications || false;
        this.unsubscribePlateValue = config.unsubscribePlateValue || '';
        this.subscribeForAnyPlateValue = config.subscribeForAnyPlateValue || false;
        this.startTimestamp = config.startTimestamp || null;
        this.endTimestamp = config.endTimestamp || null;
    }

    public get PlateValue(): string
    {
        return this.plateValue;
    }

    public get IsPlateValueExactMatch(): boolean
    {
        return this.isPlateValueExactMatch;
    }

    public get SubscribeForNotifications(): boolean
    {
        return this.subscribeForNotifications;
    }

    public get UnsubscribePlateValue(): string
    {
        return this.unsubscribePlateValue;
    }

    public get SubscribeForAnyPlateValue(): boolean
    {
        return this.subscribeForAnyPlateValue;
    }

    public get StartTimestamp(): Date | null
    {
        return this.startTimestamp;
    }

    public get EndTimestamp(): Date | null
    {
        return this.endTimestamp;
    }
}