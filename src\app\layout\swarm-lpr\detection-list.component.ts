import { Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { DetectionService } from '../../services/detecions/detection.service';
import { NotificationCategory } from 'app/shared/modules/data-layer/enum/reports/notification-category.enum';
import { Detection } from '../../shared/modules/data-layer/models/detections/detection';
import { NotificationMessageDTO } from 'app/shared/modules/data-layer/services/data-chunks/notification-message.dto';
import { SignalRService } from 'app/shared/services/signalR.service';
import { ISignalRConnection } from 'ng2-signalr';
import { DetectionPageOptions } from '../../shared/dtos/detection-page-options';
import { NavigationService, Pages } from '../../shared/services/navigation.service';
import { DetectionModalImageComponent } from './detection-modal-image/detection-modal-image.component';

@Component({
  selector: 'app-detection-list',
  templateUrl: './detection-list.component.html',
  styleUrls: ['./detection-list.component.scss'],
})
export class DetectionListComponent implements OnInit, OnDestroy {
  data: Detection[] = [];
  pageSize: number = 12;
  totalRecords: number = 0;
  currentPageIndex: number = 0;
  plateValue: string = "";
  routeEventId: string = "";
  loading: boolean = false;
  private latestSubscriptionValue: string = '';
  private ngSignalRConnection: ISignalRConnection;
  private subscriptions: Subscription[] = [];
  @ViewChild('detectionModalImage') detectionModalImage!: DetectionModalImageComponent;

  constructor(
    private detectionService: DetectionService,
    private navigationService: NavigationService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private signalRService: SignalRService) { }

  ngOnInit(): void {
    this.activatedRoute.queryParams.subscribe((params) => {
      this.plateValue = params['entityId'] || '';
    });
    this.getDetections();
  }

  onPageChange(newPageIndex: number): void {
    this.currentPageIndex = newPageIndex;
    this.getDetections();
  }

  onTrafficDetectionFilter(value: {searchValue: string, routerEventId: string}): void {
    if(value.routerEventId === '') {
      this._removeEventId();
    }
    this.plateValue = value.searchValue;
    this.getDetections();
  }

  private _removeEventId(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { entityId: null },
      queryParamsHandling: 'merge'
    });
}


  listenForDetection(): void {
    let notificationSubscription = this.signalRService.GetConnection('NotificationHub').subscribe(connection => {
      this.ngSignalRConnection = connection;

      connection.listenFor<string>('onNotify').subscribe((message: string) => {
        if (!this.plateValue || this.plateValue.length === 0) {
          return;
        }

        let parsedMessage = JSON.parse(message);
        let notificationMessage = new NotificationMessageDTO(parsedMessage).toModel();

        if (notificationMessage.header.type !== NotificationCategory.SwarmLPRNotification) {
          return;
        }

        let detection = this.detectionService.ConvertToDetection(parsedMessage.Payload);
        if (detection.plateNo.indexOf(this.plateValue) < 0) {
          return;
        }

        let alreadyExisting = this.data.filter(currentDetection => currentDetection.id === detection.id);
        if (alreadyExisting.length > 0) {
          return;
        }

        this.totalRecords++;
        this.data = [detection, ...this.data];
      });
    }, error => {
      console.error(error);
    });
    this.subscriptions.push(notificationSubscription);
  }

  getDetections(): void {
    let subscribeForNotifications: boolean = (this.plateValue && this.plateValue.length > 0) ? true : false;
    let plateValue = this.plateValue;
    this.loading = true;
    let pageOptions: DetectionPageOptions = new DetectionPageOptions({
      pageIndex: this.currentPageIndex,
      pageSize: this.pageSize,
      plateValue: plateValue,
      isPlateValueExactMatch: false,
      subscribeForNotifications: subscribeForNotifications,
      unsubscribePlateValue: this.latestSubscriptionValue
    });

    this.detectionService.getDetectionsPage(pageOptions).subscribe((detectionsResponse) => {
      this.latestSubscriptionValue = subscribeForNotifications ? plateValue : '';

      this.data = detectionsResponse.Items;
      this.totalRecords = detectionsResponse.TotalCount;
      this.loading = false;
    }, () => {
      this.loading = false;
    });
  }

  onOpenVehicleTraffic(item: Detection): void {
      let plateValue : string = item.plateNo;
      let itemId : string = item.id;
      this.navigationService.navigate(Pages.vehicleTraffic,  { plateNumber: plateValue, itemId: itemId, search: plateValue, pageIndex: this.currentPageIndex});
    }
  

  onOpenTrafficImage(item: Detection): void {
    if (this.detectionModalImage && item) {
      this.detectionModalImage.item = item;
      this.detectionModalImage.show();
    }
  }

  ngOnDestroy(): void {
    if (this.latestSubscriptionValue.length > 0) {
      let unsubscribeSubscription = this.detectionService.UnsubscribeFromNotifications(this.latestSubscriptionValue,
        false)
        .subscribe((dummyBoolean) => {

          this.subscriptions.forEach(item => item.unsubscribe());
        });

      this.subscriptions.push(unsubscribeSubscription);
    }
    else {
      this.subscriptions.forEach(item => item.unsubscribe());
    }
  }
}