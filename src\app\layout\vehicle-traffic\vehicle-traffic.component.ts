import { AfterViewInit, ChangeDetector<PERSON>ef, Component, ComponentFactoryResolver, ComponentRef, Inject, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewChild, ViewContainerRef } from "@angular/core";
import { CymbiotMarker } from "app/shared/modules/cymbiot-map/components/cymbiot-map/cymbiot-marker";
import { GisTileProvider } from "app/shared/modules/cymbiot-map/enums/gis-tile-provider.enum";
import { CymbiotMapUtilsService } from "app/shared/modules/cymbiot-map/services/cymbiot-map-utils.service";
import { IMapProjectionTransformService } from "app/shared/modules/generic-map/components/base-map-transform.service";
import { GenericMapComponent } from "app/shared/modules/generic-map/components/generic-map.component";
import { Point as CYPoint, Marker, TileSource } from 'app/shared/modules/generic-map/models/map.models';
import { EventService, EventType } from "app/shared/services/event.service";
import { Subscription } from "rxjs";

import { HostListener } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { TranslateService } from "@ngx-translate/core";
import { DetectionService } from "app/services/detecions/detection.service";
import { Guid } from "app/shared/enum/guid";
import { ToastTypes } from "app/shared/enum/toast-types";
import { DetectionMarkerComponent } from "app/shared/modules/cymbiot-map/components/cymbiot-map/detection-marker/detection-marker.component";
import { DefaultMarkerComponent } from "app/shared/modules/cymbiot-map/components/default-marker/default-marker.component";
import { DetectionData } from "app/shared/modules/cymbiot-map/models/detection-data.interface";
import { CymbiotPopupService } from "app/shared/modules/cymbiot-map/services/cymbiot-popup.service";
import { NotificationCategory } from "app/shared/modules/data-layer/enum/reports/notification-category.enum";
import { Detection } from "app/shared/modules/data-layer/models/detections/detection";
import { DownloadStatus } from "app/shared/modules/data-layer/models/detections/download-status";
import { NotificationMessageDTO } from "app/shared/modules/data-layer/services/data-chunks/notification-message.dto";
import { Arrow } from "app/shared/modules/generic-map/models/arrow";
import { SignalRService } from "app/shared/services/signalR.service";
import * as moment from 'moment';
import { ISignalRConnection } from "ng2-signalr";
import { MessageService } from "primeng/api";
import { DetectionPageOptions } from "../../shared/dtos/detection-page-options";
import { FollowComponent } from "./follow/follow.component";

@Component({
  selector: 'vehicle-traffic',
  templateUrl: './vehicle-traffic.component.html',
  styleUrls: ['./vehicle-traffic.component.scss'],
  providers: []
})
export class VehicleTrafficComponent implements OnInit, OnChanges, OnDestroy, AfterViewInit {
    public subscriptions: Subscription[] = [];
    mapTileSource: TileSource;

    markersDictionary:Map<string,CymbiotMarker>=new Map<string,CymbiotMarker>();
    markersArray : CymbiotMarker[] = [];
    plateNumber: string;
    detectionId: string;
    instanceId: string = Guid.create().toString();
    arrows: { [id: string]: Arrow } = {};
    openedMarkerPopup: DefaultMarkerComponent = null;
    detections : Detection[];
    currentIndex: number;
    showFollow: boolean;
    detectionsReversed: Detection[];
    private ngSignalRConnection: ISignalRConnection;
    private isPlateValueExactMatch: boolean = true;
    private search: string;
    private pageIndex: number;
    private originRoute: string;
    private ORIGIN_ROUTE :string = 'originRoute';

    isFirstMarkerSelected:boolean=false;
    @ViewChild('map', {static: false}) map: GenericMapComponent;
    @ViewChild('follow', {static: false}) follow: FollowComponent;
    @ViewChild('customMarkersFactory', { read: ViewContainerRef, static: false }) customMarkersFactory: ViewContainerRef;

    constructor(private cymbiotMapUtilsService : CymbiotMapUtilsService,
    @Inject('IMapProjectionTransformService') private projectionTransform: IMapProjectionTransformService,
    private eventService: EventService,
    private changeDetectorRef: ChangeDetectorRef,
    private detectionService: DetectionService,
    private activatedRoute: ActivatedRoute,
    private cymbiotPopupService: CymbiotPopupService,
    private componentFactoryResolver: ComponentFactoryResolver,
    private messageService: MessageService,
    private translateService: TranslateService,
    private signalRService: SignalRService,
    private router: Router,) {

      let indexChangingSubscription = this.cymbiotPopupService.indexChanging.subscribe(index => {

        this.markersArray.forEach(currentMarker => {

          if (!currentMarker.component)
          {
            currentMarker.generateTemplateRef([]);
          }
          let detectionMarkerComponent = <DetectionMarkerComponent>currentMarker.component;
          detectionMarkerComponent.isSelected = false;
        });

        this.currentIndex = index;
        this.downloadPhotos();
        let marker = this.markersArray[index];

        let center = this.projectionTransform.fromLatLng(marker.x, marker.y);

        marker.generateTemplateRef([]);
        let subscription = this.map.renderCompleted.subscribe((flag) => {
          marker.component.showPopUp();
          this.openedMarkerPopup = marker.component;
          subscription.unsubscribe();
        });

        this.map.setCenter(center, null, true);

        let detectionMarkerComponent = <DetectionMarkerComponent>marker.component;
        detectionMarkerComponent.isSelected = true;
        this.detectionService.DetectionIndexChanged.next(index);
      });

      this.subscriptions.push(indexChangingSubscription);
    }
  ngAfterViewInit(): void {

  }


    getTurboTable(turbotable){

      turbotable.rowSelectedSubject.subscribe((res)=>{
        let index = this.markersArray.findIndex( item => item.id == res.id);
        let center = this.projectionTransform.fromLatLng( this.markersArray[index].x,  this.markersArray[index].y);
        this.markersArray[index].generateTemplateRef([]);
        let subscription = this.map.renderCompleted.subscribe((flag) => {
          this.markersArray[index].component.showPopUp();
          this.openedMarkerPopup =  this.markersArray[index].component;
          subscription.unsubscribe();
        });
        this.map.setCenter(center, null, false);
        let detectionMarkerComponent = <DetectionMarkerComponent> this.markersArray[index].component;

      this.selectMarker(detectionMarkerComponent, this.markersArray[index],index);
        this.detectionService.DetectionIndexChanged.next(index);

      });

    }
    ngOnInit(): void {
        // TODO
      // GET Value of GisTileProvider from a new dropdown wich will have OSM or SW tile layers
      this.mapTileSource = this.cymbiotMapUtilsService.returnTileLayerSource(GisTileProvider.OSM);

      let viewPortResizeSubscription = this.eventService.observe(EventType.ViewPortResize)
        .subscribe( () => {return this.updateSize();});
      this.subscriptions.push(viewPortResizeSubscription);

      let notificationSubscription = this.signalRService.GetConnection('NotificationHub').subscribe(connection => {
            this.ngSignalRConnection = connection;

            connection.listenFor<string>('onNotify').subscribe((message:string) => {
              let parsedMessage = JSON.parse(message);
              let notificationMessage = new NotificationMessageDTO(parsedMessage).toModel();

              if(notificationMessage.header.type !== NotificationCategory.SwarmLPRNotification) {
                  return;
              }

              let detection :Detection= this.detectionService.ConvertToDetection(parsedMessage.Payload);
              if (detection.plateNo !== this.plateNumber)
              {
                return;
              }
              if(this.detections && this.detections.length){
              let alreadyExisting = this.detections.filter(currentDetection => currentDetection.id === detection.id);
              if (alreadyExisting.length > 0)
              {
                return;
              }
              }
              this.ChangeDetectionToAvoidOverlapping(detection);

              this.detections.unshift(detection);
              this.detections.sort((first, second) => {
                let firstDetectionMoment = moment(first.cameraTime);
                let secondDetectionMoment = moment(second.cameraTime);

                return firstDetectionMoment.isBefore(secondDetectionMoment) ? -1 : 1;
              });

              this.detectionsReversed.push(detection);
               this.detectionsReversed.sort((first, second) => {
                let firstDetectionMoment = moment(first.cameraTime);
                let secondDetectionMoment = moment(second.cameraTime);

                return firstDetectionMoment.isBefore(secondDetectionMoment) ? 1 : -1;
              });

              if (!this.detectionId)
              {
                this.detectionId = detection.id;
              }

              let newMarker = this.createMapItem(detection, 0);

              if (this.detectionsReversed.length > 1)
              {
                let fromDetection = this.detectionsReversed[1];

                let arrow = this.detectionService.CreateArrow(fromDetection,
                  detection);
                let arrowKey = this.detectionService.CalculateArrowKey(fromDetection);
                this.arrows[arrowKey] = arrow;
              }

              this.cymbiotPopupService.clearClusters(this.instanceId);
              this.cymbiotPopupService.registerCluster(this.instanceId, this.markersArray, this.currentIndex);

              this.forceChangeDetectionOnChildComponents();

              if (this.detections.length === 1)
              {
                this.focusOnDetection(detection.id);
              }
              else
              {
                this.showFollow = true;
              }
              this.detectionService.NewDetection.next(detection);
            });
        }, error => {
            console.error(error);
        });
      this.subscriptions.push(notificationSubscription);

      let activeRouteSubscription = this.activatedRoute.queryParams.subscribe((params) => {
        if(!params['plateNumber']){
          throw new Error("Plate number is missing");
        }

        if (!params['pageIndex'])
        {
          throw new Error("Page index is missing");
        }

        this.plateNumber = params['plateNumber'];
        this.detectionId = params['itemId'];
        this.search = params['search'];
        this.pageIndex = parseInt(params['pageIndex']);
        this.originRoute = params[this.ORIGIN_ROUTE] ? params[this.ORIGIN_ROUTE] : 'swarmLPR';

        this.getDetections();
      });
      this.subscriptions.push(activeRouteSubscription);
    }

    back():void
    {
      this.router.navigate([this.originRoute],
      { queryParams: { 'search': this.search, 'pageIndex': this.pageIndex }});
    }

    private ChangeDetectionToAvoidOverlapping(detection: Detection): void
    {
      let overlappedDetection = this.detections.filter(overlapCandidate => overlapCandidate.Latitude === detection.Latitude &&
        overlapCandidate.Longitude === detection.Longitude);
      while (overlappedDetection.length > 0)
      {
        this.detectionService.IncrementDetectionLocation(detection, 1);
        overlappedDetection = this.detections.filter(overlapCandidate => overlapCandidate.Latitude === detection.Latitude &&
          overlapCandidate.Longitude === detection.Longitude);
      }
    }

    getDetections(): void{

      let pageOptions: DetectionPageOptions = new DetectionPageOptions({
        pageIndex: 0,
        pageSize: 1000,
        plateValue: this.plateNumber,
        isPlateValueExactMatch: this.isPlateValueExactMatch,
        subscribeForNotifications: true
      });

      let detectionsSubscription = this.detectionService.getDetectionsPage(pageOptions)
        .subscribe((detectionsResponse) => {

        this.detections = detectionsResponse.Items;

        let detectionsCloneString = JSON.stringify(this.detections);
        let detectionsCloneUntyped : any[] = JSON.parse(detectionsCloneString);
        let detectionsClone : Detection[] = detectionsCloneUntyped.map((detection)=> new Detection(detection));
        this.detectionsReversed = detectionsClone.reverse();
        this.currentIndex = 0;

        this.calculateMapItems();
      });

      this.subscriptions.push(detectionsSubscription);
    }

    private createMapItem(detection:Detection, index: number):CymbiotMarker
    {
      let marker : CymbiotMarker = this.createMarker(detection, index);
      this.markersArray.push(marker);


      this.markersDictionary[detection.id] = marker;

      if (this.detectionId && this.detectionId == detection.id)
      {
        this.currentIndex = index;
        this.downloadPhotos();
        let latitude : number = detection.Latitude;
        let longitude: number = detection.Longitude;
        let center = this.projectionTransform.fromLatLng(latitude, longitude);// + 0.0005
        let centerAbove = new CYPoint();
        centerAbove.x = center.x;
        centerAbove.y = center.y + 10;//why the overshoot?
        this.map.setCenter(centerAbove, 18);
      }

      return marker;
    }



    private calculateMapItems() : void
    {
      let arrowsMap = this.detectionService.FixCoordinatesOverlapping(this.detectionsReversed);

      this.detectionsReversed.forEach((detection, index) => {

        this.createMapItem(detection, index);
      });

      arrowsMap.forEach((value:Arrow, key:string) => {
        this.arrows[key] = value;
      });

      this.cymbiotPopupService.registerCluster(this.instanceId, this.markersArray, this.currentIndex);

      this.forceChangeDetectionOnChildComponents();

      if (this.detectionId)
      {
        this.focusOnDetection(this.detectionId);
      }
      else
      {
        this.showFollow = true;
      }
    }

    private focusOnDetection(detectionId: string): void
    {
      let subscription = this.map.renderCompleted.subscribe((flag) => {

        this.markersDictionary[detectionId].generateTemplateRef([]);
        let markerComponent = this.markersDictionary[detectionId].component;
        markerComponent.showPopUp();
        this.openedMarkerPopup = markerComponent;
        subscription.unsubscribe();
      });
    }

    private downloadPhotos()
    {
      let detection = this.detections[this.currentIndex];
      if (detection.plateImageDownloaded)
      {
        this.showFollow = true;
      }

      if (!detection.plateImageDownloaded || !detection.detectionImageDownloaded)
      {

        this.detectionService.downloadPhotos(detection.id, detection.laneId).subscribe(downloadStatuses => {
          detection.plateImageDownloaded = (downloadStatuses.plateStatus == DownloadStatus.Ok);
          detection.detectionImageDownloaded = (downloadStatuses.detectionStatus == DownloadStatus.Ok);

          this.detectionService.checkForErrors(downloadStatuses);
          this.changeDetectorRef.detectChanges();
        }, error =>{
            this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error),
              detail: this.translateService.instant("detectionList.downloadFailed")});
            this.showFollow = true;
        }, () => {
          this.showFollow = true;
        });
      }
    }

    updateSize(): void {
      this.map.updateSize();
    }

    forceChangeDetectionOnChildComponents() : void {
      this.markersDictionary = {...this.markersDictionary};
      this.arrows = {...this.arrows};
      this.changeDetectorRef.detectChanges();
    }

    @HostListener('window:popstate', ['$event'])
    onBrowserBackBtnClose(event: Event) {

        event.preventDefault();
        this.back();
    }

    onMapExtentChanged(event: number[]): void {
    }

    ngOnChanges(changes: SimpleChanges): void {
    }

    createMarker(detection:Detection, index: number): CymbiotMarker {
      let marker = new CymbiotMarker({
        x: detection.Latitude,
        y: detection.Longitude
      });

      marker.id = detection.id;
      marker.generateTemplateRef = (markers: Marker[]) => {

        if (!marker.component)
        {

          let factory = this.componentFactoryResolver.resolveComponentFactory(DetectionMarkerComponent);
          let componentRef: ComponentRef<DetectionMarkerComponent> = this.customMarkersFactory.createComponent(factory);
          componentRef.instance.data = this.returnMarkerData(detection);

          componentRef.instance.instanceId = this.instanceId;
          if (this.detectionId && this.detectionId === detection.id)
          {
            componentRef.instance.isSelected = true;
          }


          let markerClickSubscription = componentRef.instance['onClick'].subscribe(() => {

              this.selectMarker(componentRef.instance,marker,index)

          });
          this.subscriptions.push(markerClickSubscription);

          let removePopupSubscription = componentRef.instance['removePopUp'].subscribe(() => {
            if (this.openedMarkerPopup)
            {
              this.openedMarkerPopup.hidePopUp();
            }
            this.openedMarkerPopup = null;
          });
          this.subscriptions.push(removePopupSubscription);

          componentRef.changeDetectorRef.detectChanges();

          marker.component = componentRef.instance;
        }

        return marker.component.getElementRef();
      };
      // marker.generateTemplateRef([]); TO DO this generates another marker and adds it below the timeline
      return marker;
    }
public selectMarker(componentRef:  DetectionMarkerComponent,marker,index): void{
    this.markersArray.forEach(currentMarker => {
     currentMarker.generateTemplateRef([]);
      let detectionMarkerComponent = <DetectionMarkerComponent>currentMarker.component;
        detectionMarkerComponent.isSelected = false;
    });

        if (this.openedMarkerPopup) {
          this.openedMarkerPopup.togglePopUp();
        }
        this.openedMarkerPopup = componentRef;
        let center = this.projectionTransform.fromLatLng(marker.x, marker.y);

        this.map.setCenter(center, null, true);

        this.cymbiotPopupService.initializeIndex(this.instanceId, marker.id, index);
        componentRef.isSelected = true;
        this.currentIndex = index;
        this.downloadPhotos();

        this.detectionService.DetectionIndexChanged.next(index);

}
    returnMarkerData(element: Detection): DetectionData  {

      return {
        detection: element,
        instanceId : this.instanceId,
        addEvent: null
      };
    }

    ngOnDestroy(): void {

        this.cymbiotPopupService.clearClusters(this.instanceId);

        let unsubscribeSubscription = this.detectionService.UnsubscribeFromNotifications(this.plateNumber,
          this.isPlateValueExactMatch)
          .subscribe((dummyBoolean) => {

          this.subscriptions.forEach(item => item.unsubscribe());
        });

        this.subscriptions.push(unsubscribeSubscription);
    }
}
