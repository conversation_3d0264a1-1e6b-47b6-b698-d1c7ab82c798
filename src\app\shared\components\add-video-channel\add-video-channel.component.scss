@import '~styles/app';

// New color variables
$purple-primary: #7C4DFF;
$purple-light: #9E7BFF;
$purple-dark: #5C35CC;
$white: #FFFFFF;
$gray-100: #F8F9FA;
$gray-200: #E9ECEF;
$gray-300: #DEE2E6;
$gray-400: #CED4DA;
$gray-500: #ADB5BD;
$gray-600: #6C757D;
$gray-700: #495057;
$gray-800: #343A40;
$gray-900: #212529;

.camera-channels-inner {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  background: $white;
  border-radius: 16px;
  
  &.has-actions {
    height: 100%;
  }

  .header-section {
    margin-bottom: 1.5rem;

    .main-title {
      font-size: 2rem;
      font-weight: 600;
      color: $purple-primary;
      margin: 0;
    }
    
    .monitor-title {
      font-size: 1.25rem;
      color: $gray-600;
      margin-top: 0.5rem;
    }
  }

  .search-section {
    margin-bottom: 1.5rem;

    .search-box {
      position: relative;
      margin-bottom: 1rem;

      .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: $gray-500;
      }

      input {
        width: 100%;
        padding: 0.875rem 1rem 0.875rem 2.75rem;
        border: 1px solid $gray-200;
        border-radius: 12px;
        background: $gray-100;
        color: $gray-800;
        font-size: 1rem;
        transition: all 0.2s ease;

        &:focus {
          outline: none;
          border-color: $purple-light;
          box-shadow: 0 0 0 3px rgba($purple-primary, 0.1);
        }

        &::placeholder {
          color: $gray-500;
        }
      }
    }

    .status-filter {
      display: flex;
      gap: 0.75rem;

      .status-btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 12px;
        background: $gray-100;
        color: $gray-700;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        i {
          margin-right: 0.5rem;
        }

        &.active {
          background: $purple-primary;
          color: $white;
        }

        &:hover:not(.active) {
          background: $gray-200;
        }
      }
    }
  }

  .content-section {
    flex: 1;
    overflow-y: auto;
    padding-right: 0.5rem;
    
    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: $gray-100;
    }

    &::-webkit-scrollbar-thumb {
      background: $gray-500;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: $gray-600;
    }

    .section-toggle {
      margin-bottom: 1rem;

      .toggle-btn {
        width: 100%;
        padding: 1rem 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: $gray-100;
        border: none;
        border-radius: 12px;
        color: $gray-800;
        font-weight: 500;
        transition: all 0.2s ease;

        &:hover, &.active {
          background: $gray-200;
        }

        i {
          color: $gray-600;
        }
      }
    }

    .cameras-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1rem;
      padding: 0.5rem;

      &.hidden {
        display: none;
      }

      .camera-card {
        background: $white;
        border-radius: 12px;
        padding: 1.25rem;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        border: 1px solid $gray-200;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 16px rgba($gray-800, 0.08);
        }

        .camera-status {
          margin-bottom: 1rem;
          font-size: 1.5rem;
          
          &.online {
            color: #4CAF50;
          }
          
          &.offline {
            color: #F44336;
          }
        }

        .camera-info {
          h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
            font-weight: 500;
            color: $gray-800;
          }

          .status-badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: 8px;
            text-transform: capitalize;
            
            &.online {
              background: rgba(#4CAF50, 0.1);
              color: #2E7D32;
            }
            
            &.offline {
              background: rgba(#F44336, 0.1);
              color: #C62828;
            }
          }
        }

        .selection-indicator {
          position: absolute;
          top: 0.75rem;
          right: 0.75rem;
          height: 1.25rem;
          width: 1.25rem;
          border: 2px solid $gray-300;
          border-radius: 50%;

          &.selected {
            background: $purple-primary;
            border-color: $purple-primary;
          }
        }
      }
    }

    .groups-view {
      &.hidden {
        display: none;
      }

      ::ng-deep {
        .p-accordion {
          .p-accordion-header {
            .p-accordion-header-link {
              background: $gray-100;
              border: none;
              border-radius: 12px;
              margin-bottom: 0.75rem;
              padding: 1rem 1.5rem;
              color: $gray-800;
              font-weight: 500;
              
              &:hover {
                background: $gray-200;
              }
            }
          }

          .p-accordion-content {
            background: transparent;
            border: none;
            padding: 1rem 0;
          }
        }
      }

      .group-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .group-name {
          font-weight: 500;
          color: $gray-800;
        }

        .group-count {
          font-size: 0.875rem;
          color: $gray-600;
        }
      }
    }
  }

  .footer-section {
    margin-top: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1.5rem;
    border-top: 1px solid $gray-200;

    .btn-cancel, .btn-save {
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      font-weight: 500;
      transition: all 0.2s ease;
      font-size: 0.875rem;

      i {
        margin-right: 0.5rem;
      }
    }

    .btn-cancel {
      background: $gray-100;
      border: none;
      color: $gray-700;

      &:hover {
        background: $gray-200;
      }
    }

    .btn-save {
      background: $purple-primary;
      border: none;
      color: $white;

      &:hover {
        background: $purple-dark;
      }
    }

    .layout-section {
      display: flex;
      gap: 1rem;
      align-items: center;

      .layout-select {
        select {
          padding: 0.75rem 1rem;
          border: 1px solid $gray-200;
          border-radius: 12px;
          background: $gray-100;
          color: $gray-800;
          min-width: 200px;
          font-size: 0.875rem;

          &:focus {
            outline: none;
            border-color: $purple-primary;
            box-shadow: 0 0 0 3px rgba($purple-primary, 0.1);
          }
        }
      }
    }
  }

  .video-wall-settings {
    margin-top: 1rem;
    
    .settings-content {
      padding: 1rem;
      background: $gray-100;
      border-radius: 12px;
      margin-top: 0.5rem;

      &.hidden {
        display: none;
      }

      .setting-group {
        margin-bottom: 1rem;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          display: block;
          font-weight: 500;
          margin-bottom: 0.5rem;
          color: $gray-700;
        }

        .layout-options,
        .mode-options {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;

          button {
            padding: 0.5rem 1rem;
            border: 1px solid $gray-300;
            border-radius: 8px;
            background: $white;
            color: $gray-700;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: $gray-200;
            }

            &.active {
              background: $purple-primary;
              color: $white;
              border-color: $purple-primary;
            }
          }
        }
      }
    }
  }
}