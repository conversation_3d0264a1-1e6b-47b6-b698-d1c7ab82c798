import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';
import { CymsidebarService } from 'app/shared/components/cym-sidebar/cym-sidebar.service';
import { TablePaginatorEvent } from 'app/shared/components/ng-turbo-table/models/table-paginator-event.interface';
import { ElementTableCell, TableActionsComponent, TableCell, TableColumnProperties } from 'app/shared/components/ng-turbo-table/models/table.models';
import { NgTurboTableComponent } from 'app/shared/components/ng-turbo-table/ng-turbo-table.component';
import { Guid } from 'app/shared/enum/guid';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { NotificationCategory } from 'app/shared/modules/data-layer/enum/reports/notification-category.enum';
import { Detection } from 'app/shared/modules/data-layer/models/detections/detection';
import { DownloadStatus } from 'app/shared/modules/data-layer/models/detections/download-status';
import { LocationInfringement } from 'app/shared/modules/data-layer/models/detections/location-infringement';
import { NotificationMessageDTO } from 'app/shared/modules/data-layer/services/data-chunks/notification-message.dto';
import { SignalRService } from 'app/shared/services/signalR.service';
import * as moment from 'moment';
import { ISignalRConnection } from 'ng2-signalr';
import { MessageService } from 'primeng/api';
import { Subscription } from 'rxjs';
import { DetectionPageOptions } from '../../shared/dtos/detection-page-options';
import {DetectionService} from "app/services/detecions/detection.service";


@Component({
  selector: 'detections-table',
  templateUrl: './detections-table.component.html',
  styleUrls: ['./detections-table.component.scss'],
  providers: [CymsidebarService],

})
export class DetectionsTableComponent implements OnInit, OnDestroy, OnChanges {

  @ViewChild('platePhotoTemplate', {static: true}) platePhotoTemplate: TemplateRef<TableActionsComponent>;
  @ViewChild('actionsTemplate', {static: true}) actionsTemplate: TemplateRef<TableActionsComponent>;
  @ViewChild('statusTemplate', {static: true}) statusTemplate: TemplateRef<TableActionsComponent>;
  @ViewChild('photosModal', {static:false}) photosModal: AppModal;
  @ViewChild("turboTable", {static:false}) turboTable: NgTurboTableComponent;

  showTableCaption: boolean = true;

  columns: TableColumnProperties[] = [];
  data: { [columnField: string]: TableCell | string }[] = [];
  private subscriptions: Subscription[] = [];
  pageSize: number = 10;
  totalRecords: number = 0;
  pageIndex: number = 0;
  plateValue : string = "";
  showCheckboxes : boolean = false;
  currentRecord: { [columnField: string]: TableCell | string };
  actionText: string;
  private percent: string = " %";
  private parkingStatuses: Map<string, boolean> = new Map<string, boolean>();
  private ngSignalRConnection: ISignalRConnection;
  private allPlateValuesWildCard : string = "*";

  constructor(
    private detectionService: DetectionService,
    private translateService: TranslateService,
    private messageService: MessageService,
    private router: Router,
    private route: ActivatedRoute,
    private signalRService: SignalRService,
    ) {

    }

  ngOnInit(): void {

    this.generateColumns();

    let notificationSubscription = this.signalRService.GetConnection('NotificationHub').subscribe(connection => {
          this.ngSignalRConnection = connection;

          connection.listenFor<string>('onNotify').subscribe((message:string) => {

            let parsedMessage = JSON.parse(message);
            let notificationMessage = new NotificationMessageDTO(parsedMessage).toModel();

            if(notificationMessage.header.type !== NotificationCategory.SwarmLPRNotification) {
                return;
            }

            let detection = this.detectionService.ConvertToDetection(parsedMessage.Payload);
            if (this.plateValue.length > 0 && detection.plateNo.indexOf(this.plateValue) < 0)
            {
              return;
            }

            let alreadyExisting = this.data.filter(currentDetection => currentDetection.id === detection.id);
            if (alreadyExisting.length > 0)
            {
              return;
            }

            let record = this.ConvertToRecord(detection);
            let records = this.data;
            records.unshift(record);
            this.totalRecords++;

            let recordsToDelete = records.length - this.pageSize;
            if (recordsToDelete > 0)
            {
              records.splice(this.pageSize, recordsToDelete);
            }

            this.data = records;

            this.turboTable.appendToSelection(record);

            this.resolveLocationInfringement(detection);
          });
        }, error => {
            console.error(error);
        });
      this.subscriptions.push(notificationSubscription);

      let activeRouteSubscription = this.route.queryParams.subscribe((params) => {
        if(params['search']){
          this.plateValue = params['search'];
        }

        if (params['pageIndex'])
        {
          this.pageIndex = parseInt(params['pageIndex']);

        }

        this.getDetections();
      });
    this.subscriptions.push(activeRouteSubscription);
  }

  ngOnChanges(changes: SimpleChanges): void{


  }

  onTurboTablePaginatorEvent(event: TablePaginatorEvent): void {

    this.pageIndex = event.first / this.pageSize;

    this.getDetections();
  }

  onSearchEvent(value: string)
  {
    this.plateValue = value;

    if (this.plateValue.length == 0 || this.plateValue.length > 1)
    {
      this.getDetections();
    }
  }

  onActionEvent(plateValue: string){

  }

  onModalConfirm(){
    this.photosModal.closeModal();
    this.currentRecord = null;
  }

  onPlatePhotoClick(record)
  {
    this.currentRecord = record;
    this.photosModal.openModal();
  }

  getDetections(): void{
    const subscribeForNotifications: boolean = true;

    let pageOptions: DetectionPageOptions = new DetectionPageOptions({
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      plateValue: this.plateValue,
      isPlateValueExactMatch: false,
      subscribeForNotifications: subscribeForNotifications,
      unsubscribePlateValue: "",
      subscribeForAnyPlateValue: true
    });

    this.detectionService.getDetectionsPage(pageOptions).subscribe((detectionsResponse) => {

      this.turboTable.setPageIndex(this.pageIndex);
      this.turboTable.setSearchValue(this.plateValue);

        let records : { [columnField: string]: TableCell | string }[] = [];
        detectionsResponse.Items.forEach((item) => {
          let record = this.ConvertToRecord(item);
          records.push(record);

          this.resolveLocationInfringement(item);
        });

        this.data = records;
        this.totalRecords = detectionsResponse.TotalCount;
      });
  }

  resolveLocationInfringement(detection: Detection)
  {
    this.detectionService.getLocationInfringement(detection.plateNo, detection.latitude, detection.longitude)
    .subscribe((locationInfringement:LocationInfringement) => {
      this.parkingStatuses[detection.id] = !locationInfringement.isAccessFeeNotPayed;
    });
  }

  downloadPhotos(item: { [columnField: string]: TableCell | string }) :void{

    let itemId : string = item.id.toString();
    let laneId: Guid = Guid.parse( item.laneId.toString());

    this.detectionService.downloadPhotos(itemId, laneId).subscribe(downloadStatuses => {
      item.plateImageDownloaded = (downloadStatuses.plateStatus == DownloadStatus.Ok).toString();
      item.detectionImageDownloaded = (downloadStatuses.detectionStatus == DownloadStatus.Ok).toString();

      this.detectionService.checkForErrors(downloadStatuses);
    }, error =>{
        this.messageService.add({severity: 'error', summary: this.translateService.instant(ToastTypes.error),
          detail: this.translateService.instant("detectionList.downloadFailed")});
    });
  }

  openVehicleTraffic(item: { [columnField: string]: TableCell | string }):void{

    let plateNumber : string = item.plateNo.toString();
    let itemId : string = item.id.toString();

    this.router.navigate(['/vehicleTraffic'],
      { queryParams: { 'plateNumber': plateNumber, 'itemId': itemId,
      'search': this.plateValue, 'pageIndex': this.pageIndex, 'originRoute': 'parkingLPR' }});
  }

  isUnpaid(item: { [columnField: string]: TableCell | string }): boolean
  {
    let detectionId = <string>item["id"];

    return !this.parkingStatuses[detectionId];
  }

  isPaid(item: { [columnField: string]: TableCell | string }): boolean
  {
    let detectionId = <string>item["id"];

    return this.parkingStatuses[detectionId];
  }

  generateColumns(): void {

     let plateNumberColumn = new TableColumnProperties({
      field: "plateNo",
      header: this.translateService.instant("detectionList.licensePlate"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(plateNumberColumn);

    let actionsColumn = new TableColumnProperties({
      field: "actions",
      header: this.translateService.instant("detectionList.actions"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(actionsColumn);

    let statusColumn = new TableColumnProperties({
      field: "status",
      header: this.translateService.instant("parkingDetections.status"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(statusColumn);

    let platePhotoColumn = new TableColumnProperties({
      field: "platePhoto",
      header: this.translateService.instant("detectionList.platePhoto"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(platePhotoColumn);

    let laneColumn = new TableColumnProperties({
      field: "cameraName",
      header: this.translateService.instant("detectionList.camera"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,

    })
    this.columns.push(laneColumn);

    let vehicleBrandColumn = new TableColumnProperties({
      field: "vehicleBrand",
      header: this.translateService.instant("detectionList.brand"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(vehicleBrandColumn);

    let vehicleColorColumn = new TableColumnProperties({
      field: "vehicleColor",
      header: this.translateService.instant("detectionList.color"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(vehicleColorColumn);

    let plateCountryColumn = new TableColumnProperties({
      field: "plateCountry",
      header: this.translateService.instant("detectionList.country"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(plateCountryColumn);



    let vehicleModelColumn = new TableColumnProperties({
      field: "vehicleModel",
      header: this.translateService.instant("detectionList.vehicleModel"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(vehicleModelColumn);

    let vehicleTypeColumn = new TableColumnProperties({
      field: "vehicleType",
      header: this.translateService.instant("detectionList.vehicleType"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(vehicleTypeColumn);

    let cameraTimeColumn = new TableColumnProperties({
      field: "cameraTime",
      header: this.translateService.instant("detectionList.cameraTime"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(cameraTimeColumn);

    let plateConfidenceColumn = new TableColumnProperties({
      field: "plateConfidence",
      header: this.translateService.instant("detectionList.plateConfidence"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(plateConfidenceColumn);

    let vehicleConfidenceColumn = new TableColumnProperties({
      field: "vehicleConfidence",
      header: this.translateService.instant("detectionList.vehicleConfidence"),
      visibile: true,
      frozen: false,
      isFilterable: false,
      sortable: false,
    });
    this.columns.push(vehicleConfidenceColumn);



  }

  private ConvertToRecord(item: Detection): { [columnField: string]: TableCell | string }
  {
    let record : { [columnField: string]: TableCell | string } = {};
    record.id = item.id;
    record.laneId = item.laneId.toString();
    record.plateNo = item.plateNo;
    record.plateCountry = item.plateCountry;
    record.plateConfidence = (item.plateConfidence).toFixed(0) + this.percent;
    record.vehicleBrand = item.vehicleBrand;
    record.vehicleModel = item.vehicleModel;
    record.vehicleType = item.vehicleType;
    record.vehicleColor = item.vehicleColor;
    record.vehicleConfidence = item.vehicleConfidence.toFixed(0) + this.percent;
    record.cameraName = item.cameraName;
    record.plateImageDownloaded = item.plateImageDownloaded.toString();
    record.detectionImageDownloaded = item.detectionImageDownloaded.toString();
    record.platePhotoUrl = this.detectionService.calculatePhotoUrl(item.id, item.laneId, true);
    record.detectionPhotoUrl = record.platePhotoUrl

    record.cameraTime = item.cameraTime ? item.cameraTime.toString() : '';

    let platePhotoCell: TableCell = new ElementTableCell(this.platePhotoTemplate);
    record['platePhoto'] = platePhotoCell;

    let actionsCell: TableCell = new ElementTableCell(this.actionsTemplate);
    record['actions'] = actionsCell;

    let statusCell: TableCell = new ElementTableCell(this.statusTemplate);
    record['status'] = statusCell;

    return record;
  }

  ngOnDestroy(): void {

    let unsubscribeSubscription = this.detectionService.UnsubscribeFromNotifications(this.allPlateValuesWildCard,
      true)
      .subscribe((dummyBoolean) => {

      this.subscriptions.forEach(item => item.unsubscribe());
    });

    this.subscriptions.push(unsubscribeSubscription);

  }
}
