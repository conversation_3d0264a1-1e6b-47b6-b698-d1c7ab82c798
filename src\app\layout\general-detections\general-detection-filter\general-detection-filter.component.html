<div class="filters-container">
    <div class="filters-wrapper">
        <div class="title">
            {{ 'generalDetections.generalDetections' | translate }}
        </div>
        <div class="filters-content">
            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-search green"></i>
                    <input type="text" pInputText (ngModelChange)="searchValue1Changed($event)"
                        placeholder="{{ 'search...' | translate }}" [(ngModel)]="searchValue1"
                        class="input-element input-filter"
                        pTooltip="{{ 'generalDetections.searchFirstField' | translate }}" tooltipPosition="top" />
                </div>
            </div>

            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-search green"></i>
                    <input type="text" pInputText (ngModelChange)="searchValue2Changed($event)"
                        placeholder="{{ 'search...' | translate }}" [(ngModel)]="searchValue2"
                        class="input-element input-filter" [disabled]="!searchValue1"
                        pTooltip="{{ 'generalDetections.searchSecondField' | translate }}" tooltipPosition="top" />
                </div>
            </div>

            <div class="filter-element-container">
                <div class="filter-element">
                    <span>{{ 'resourceGroup' | translate }}</span>
                    <p-dropdown [options]="resourceGroups" [filter]="true" [(ngModel)]="selectedResourceGroup"
                                (ngModelChange)="selectedGroupChanged($event)"
                                pTooltip="{{ 'generalDetections.selectResourceGroup' | translate }}" tooltipPosition="top">
                        <ng-template let-item pTemplate="selectedItem">
                            <span>{{item.name}}</span>
                        </ng-template>
                        <ng-template let-item pTemplate="item">
                            <div class="ui-helper-clearfix">
                                <div>{{item.name}}</div>
                            </div>
                        </ng-template>
                   </p-dropdown>
                </div>
            </div>

            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-calendar green"></i>
                    <p-calendar [(ngModel)]="startDate" [showTime]="true" [showSeconds]="true" [showIcon]="false"  [showClear]="true"  [readonlyInput]="true"  
                    dateFormat="dd/mm/yy" placeholder="{{ 'generalDetections.startDate' | translate }}" (onSelect)="onStartTimeStampChanged($event)" appendTo="body" (onClear)="onStartTimeStampChanged(null)"
                    pTooltip="{{ 'generalDetections.selectStartDate' | translate }}" tooltipPosition="top">
                 </p-calendar>
                </div>
            </div>

            <div class="filter-element-container">
                <div class="filter-element">
                    <i class="fa fa-calendar green"></i>
                    <p-calendar [(ngModel)]="endDate" [showTime]="true" [showSeconds]="true" [showIcon]="false"  [showClear]="true" [readonlyInput]="true"  
                    dateFormat="dd/mm/yy" placeholder="{{ 'generalDetections.endDate' | translate }}" (onSelect)="onEndTimeStampChanged($event)" appendTo="body" (onClear)="onEndTimeStampChanged(null)"
                    pTooltip="{{ 'generalDetections.selectEndDate' | translate }}" tooltipPosition="top">
              </p-calendar>
                </div>
            </div>

            <div class="filter-element action-buttons">
                <div class="filter-element export-buttons">
                    <p-splitButton #exportBtn 
                        [label]="exportLoading ?  ( 'exporting' | translate) : ('export' | translate)" [disabled]="exportLoading"
                        [icon]="exportLoading ? 'pi pi-spin pi-spinner' : 'fa fa-download'" (onClick)="onDefaultClick($event)" [model]="items"
                        appendTo="body" >
                    </p-splitButton>
                </div>
            </div>
        </div>
    </div>
</div>
