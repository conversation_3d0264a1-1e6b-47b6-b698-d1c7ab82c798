import { AuditService, AuditPageRequest } from './../../services/audit/audit.service';
import { Audit } from './models/audit.model';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-audit',
  templateUrl: './audit.component.html',
  styleUrls: ['./audit.component.scss']
})
export class AuditComponent implements OnInit, OnDestroy {
  audits: Audit[] = [];
  loading = false;
  currentPageIndex = 0;
  pageSize = 12;
  totalRecords = 0;

  // Filter parameters
  startTimestamp: string | null = null;
  endTimestamp: string | null = null;
  searchMessage: string = '';
  searchIp: string = '';
  searchUser: string = '';

  private destroy$ = new Subject<void>();

  constructor(private auditService: AuditService) { }

  ngOnInit(): void { }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getAudits(): void {
    this.loading = true;
    const request: AuditPageRequest = { } as AuditPageRequest;
    request.PageIndex = this.currentPageIndex;
    request.PageSize = this.pageSize;

    if(this.startTimestamp) {
      request.StartTimestamp = this.startTimestamp;
    }

    if (this.endTimestamp) {
      request.EndTimestamp = this.endTimestamp;
    }
    if (this.searchMessage) {
      request.Message = this.searchMessage;
    }
    if (this.searchIp) {
      request.IP = this.searchIp;
    }
    if (this.searchUser) {
      request.User = this.searchUser;
    }

    this.auditService.getAudits(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe(response => {
        if (response && response.items) {
          this.audits = response.items.map(item => {
            const processedItem = {
              ...item,
              id: item.id || '',
              user: item.user || '',
              message: item.message || '',
              timestamp: item.timestamp || '',
              ip: item.ip || ''
            };
            return processedItem;
          });
          this.totalRecords = response.totalCount || 0;
        } else {
          this.audits = [];
          this.totalRecords = 0;
        }

        this.loading = false;
      }, error => {
        this.loading = false;
      });
  }

  onPageChange(newPageIndex: number): void {
    this.currentPageIndex = newPageIndex;
    this.getAudits();
  }

  /**
   * Update filter parameters from the filter component
   */
  onFilterChange(filters: any): void {
    if (!filters) {
      return;
    }
    this.searchMessage = filters.searchQuery || '';
    this.searchUser = filters.user || '';
    this.searchIp = filters.ip || '';
    this.startTimestamp = filters.startDate;
    this.endTimestamp = filters.endDate;
    this.currentPageIndex = 0;
    this.getAudits();
  }
}
