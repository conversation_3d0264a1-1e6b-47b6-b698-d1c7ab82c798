import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, QueryList, SimpleChanges, ViewChild, ViewChildren, ViewContainerRef } from '@angular/core';
import { MapUtilsService } from 'app/layout/app-map/services/map-utils.service';
import { GlobalAction } from 'app/shared/models/global-action.interface';
import { Detection } from 'app/shared/modules/data-layer/models/detections/detection';
import { LocationInfringement } from 'app/shared/modules/data-layer/models/detections/location-infringement';
import { Event } from 'app/shared/modules/data-layer/models/event';
import * as moment from 'moment';
import { Subscription } from 'rxjs';
import { NavigationOptions } from '../../enums/navigation-options.enum';
import { ClusterInfo } from '../../models/cluster-info.interface';
import { CymbiotPopupService } from '../../services/cymbiot-popup.service';
import {DetectionService} from "app/services/detecions/detection.service";
import { PlayersService } from '../../../../services/players.service';
import { Guid } from '../../../../enum/guid';

@Component({
  selector: 'app-detection-popup',
  templateUrl: './detection-popup.component.html',
  styleUrls: ['./detection-popup.component.scss']
})
export class DetectionPopupComponent implements OnInit, OnChanges, OnDestroy {

  @Input('detection') detection: Detection;
  @Input('addEvent') addEvent: Event;
  @Input('instanceid') instanceId: string;
  @Output('removePopUp') removePopUp: EventEmitter<boolean> = new EventEmitter();

  @ViewChild('wrapper', {static: false}) wrapper: ElementRef;
  @ViewChildren('actionViewContainer', {read: ViewContainerRef}) public actionViewContainer: QueryList<ViewContainerRef>;

  resourceActions: GlobalAction[];
  clusterInfo: ClusterInfo;
  private navigationOptions = NavigationOptions;
  appliedFilters: string[] = [];
  detectionPhotoUrl: string;
  canPlayback: boolean;
  playbackStarted: boolean;

  private isPollutionStandardBroken: boolean;
  private isAccessFeeNotPayed: boolean;
  private subscriptions : Subscription[] =[];

  constructor(
    private cymbiotPopupService: CymbiotPopupService,
    private mapUtilsService: MapUtilsService,
    private detectionService: DetectionService,
    private playerService: PlayersService,
    private changeDetectorRef: ChangeDetectorRef
  ) { }


  ngOnInit(): void {
    this.appliedFilters = this.mapUtilsService.returnFilterForMapWithInstanceId(this.instanceId);
    this.initClusterData();

    if (this.detection.recordingChannelId)
    {
      this.canPlayback = true;
    }

    let cameraTimeMoment = moment(this.detection.cameraTime);
    this.detection.cameraTimeDisplayValue = cameraTimeMoment.format("MMM DD YYYY, HH:mm");

    this.detectionPhotoUrl = this.detectionService.calculatePhotoUrl(this.detection.id, this.detection.laneId, false);

    var infringementSubscription = this.detectionService.getLocationInfringement(this.detection.plateNo,
      this.detection.latitude, this.detection.longitude)
    .subscribe((infringement:LocationInfringement) => {

      this.isPollutionStandardBroken = infringement.isPollutionStandardBroken;
      this.isAccessFeeNotPayed = infringement.isAccessFeeNotPayed;

      this.changeDetectorRef.detectChanges();
    });

    this.subscriptions.push(infringementSubscription);

    let openChannelSubscription = this.playerService.ChannelOpend.subscribe(res => {
      if (!this.playbackStarted)
      {
        this.playbackStarted = true;
        this.playerService.setPlaybackTime(res.playerId, new Date(this.detection.cameraTime));
      }
    });
    this.subscriptions.push(openChannelSubscription);
  }

  ngOnChanges(changes: SimpleChanges): void {

    if(changes.detection && changes.detection.currentValue){
      this.initClusterData();
    }
  }

  ngOnDestroy(): void {
    this.playerService.clear();
    this.subscriptions.forEach(item => item.unsubscribe());
  }

  public calculatePhotoId(): string
  {
    return "detectionPhoto_" + this.detection.id;
  }

  private initClusterData(): void{
    this.clusterInfo = this.cymbiotPopupService.getClusterInfo(this.instanceId, this.detection.id);
  }

  executeAction(): void {
  }

  private navigateTo(navigationOption: NavigationOptions): void {
    switch(navigationOption){
      case(NavigationOptions.NEXT):
        this.cymbiotPopupService.nextButDontShow(this.instanceId, this.detection.id);
        break;
      case(NavigationOptions.PREV):
        this.cymbiotPopupService.prevButDontShow(this.instanceId, this.detection.id);
        break;
      default:
        throw new Error("Navigation option not supported");
    }
  }

  private playback(recordingChannelId: Guid)
  {
    this.playerService.clear();
    this.playbackStarted = false;
    this.playerService.openChannel(recordingChannelId.toString());
  }
}