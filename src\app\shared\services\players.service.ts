import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { webrtcData } from 'app/interfaces/webrtc.interface';
import { StatusState } from 'app/services/data_store/status.model';
import { AuthStoreService } from 'app/services/store_servcies/auth-store.service';
import { ResourceStoreService } from 'app/services/store_servcies/resource-store.service';
import { StatusService } from 'app/services/store_servcies/status.service';
import { PopupContainerService } from 'app/shared/components/popup-container/popup-container.service';
import { SettingsService } from 'app/shared/services/settings.service';
import * as fromAppReducers from 'app/store/app.reducers';
import { MessageService } from 'primeng/api';
import { Observable, Subject, forkJoin as observableForkJoin } from 'rxjs';
import { take, timeout } from 'rxjs/operators';
import { Guid } from '../enum/guid';
import { ToastTypes } from '../enum/toast-types';
import { PlayerModel } from '../models/player.model';
import { PlayerComponent } from './../components/player/player.component';

import { DataService } from './data.service';
import {ApiCommands} from "app/shared/enum/enum";
@Injectable()
export class PlayersService {

    _players: PlayerModel[] = [];
    _playerCount = 0;
    _newPlayerCount = new Subject<number>();
    ChannelOpend = new Subject<{playerId:string,channelName:string,channelId:string}>();
    ChannelClosed = new Subject<{ playerId: string, channelId: string }>();
    //TODO: decouple from PlayerComponent
    closeChannelTour=new Subject<PlayerComponent>();
    PlaybackTimeChanged = new Subject<{ playerId: string, time: Date }>();
    openVideoChannelDlg = new Subject<string>();
    counterPlayers = 0;
    _subscription = [];
    initialChannelTour =new Subject<{channelTour:string,player:string}>();
    openChannelSubject=new Subject<{id:string,isChannelTour:boolean,playerId:string}>();
    openChannelPlaybackAPISubject=new Subject<{channelId:string, speed: number, time: Date,playerId:string}>();
    setBackToLiveSubject=new Subject<{playerId:string}>();
    camerasAlreadyOpen: string[] = [];
    draggedChannelData = new Subject<string>();
    playersInitialized = new Subject<Guid[]>();


    constructor(
        private dataService: DataService,
        private popupContainerSerivce: PopupContainerService,
        private store: Store<fromAppReducers.AppState>,
        private settingsService: SettingsService,
        private messageService: MessageService,
        private _i18n: TranslateService,
        private resourceStoreService: ResourceStoreService,
        private statusStoreService: StatusService,
        private authStoreService: AuthStoreService
    ) {
        this.closeChannelTour.subscribe(res=>{
            this._playerCount=0;
        })
        this.authStoreService.getAuthState().subscribe(res => {
            if (res.action === fromAppReducers.StoreActions.auth.LOGOUT) {
                this.onDestroy();
            }
          })
        this.statusStoreService.getState().subscribe((res: StatusState) => {
            if (res !== null) {
                this.checkState(res);
            }
        })

    }

    public findPlayer(id: string): PlayerModel {

        return this._players.find(p => { return p.playerId === id;});
    }

    public openVideoChannelDialog(playerId: string): void {
        this.openVideoChannelDlg.next(playerId);
    }

    checkState(statuses: StatusState): void {
        if (statuses === null){
            return;
        }
        for (let id in statuses) {
            this._players.forEach((player) => {
                if (!player.isActive) {
                    return false;
                }

            });
        }
    }

    StreamerErrorCount = 0;
    public setStremerError(player: PlayerComponent): void {
        this.StreamerErrorCount++;
        setTimeout(() => {
            player.reopenChannel();
            this.StreamerErrorCount--;
        }, this.StreamerErrorCount * 1000);
    }

    public setPlayersRef(players: PlayerModel[]): void  {
        let playerIds : Guid[] = [];
        if (players && players.length > 0) {
            players.forEach(player => {
                if (player && player.playerId) {
                    this._players.push(player);
                    let playerId = Guid.parse(player.playerId);
                    if (playerId) {
                        playerIds.push(playerId);
                    }
                }
            });
            this._playerCount = this._players.length;

            if (playerIds.length > 0) {
                this.playersInitialized.next(playerIds);
            }
        }
    }

    //TODO at line 108, deserialize the string to a model type (probably ChannelTour)
    public loadPlayers(): void {
        this._players.forEach(player => {
            let channel = this.settingsService.get('player_' + player.playerIndex);
            let channelTour=this.settingsService.get('channel_tour_player_' + player.playerIndex);
            if (channel && channel !== "") {
                this.openChannel(channel, player.playerId);
            }else if(channelTour && channelTour !== ""){
                this.initialChannelTour.next({channelTour,player:player.playerId});
            }
        });
    }

    public setChannelClosed(player: PlayerModel, isClearSettings: boolean): void  {
        if (isClearSettings) {
        this.settingsService.set('player_' + player.playerIndex, "");
        }
        this.ChannelClosed.next({ playerId: player.playerId, channelId: player.channelId });
    }

    //TODO receive the parameter 'data' as model type, not string (probably ChannelTour)
    public setChannelTour(playerId: string, data:string): void {

        let player=this.findPlayer(playerId)
        this.settingsService.set('channel_tour_player_' + player.playerIndex, data);
        this.settingsService.set('player_' + player.playerIndex, "");
        this.setChannelClosed(player, true);
    }

    public setChannelOpend(playerId: string,isChannelTour?: boolean,channelId?:string): void {
        let player=this.findPlayer(playerId)
        if(!isChannelTour){
            this.settingsService.set('player_' + player.playerIndex,channelId);
            this.settingsService.set('channel_tour_player_' + player.playerIndex, "");
        }
        this.ChannelOpend.next({"playerId":player.playerId,"channelName":player.channelName,"channelId":player.channelId});
    }

    public setPlaybackTime(playerId: string, time: Date): void {
        this.PlaybackTimeChanged.next({ playerId: playerId, time: time });
    }

    public clear(): void {
        this.counterPlayers = 0;
        this._players = [];
        this._playerCount = 0;
    }

    public getFirstPlayer(): PlayerModel {
        return this._players[0];
    }

    public openChannelPlayback(channelId: string, speed: number, time: Date, playerId: string): Observable<string> {
         let channelIsOpened = this.channelIsOpened(channelId);

         if(!channelIsOpened){

            if (!playerId || this._playerCount === 0) {
                this.popupContainerSerivce.createContainer(channelId);
                return Observable.create(observer => {
                    observer.next("done");
                    observer.complete();
                });
            } else {
                let player = this.getAvailablePlayer(playerId);
                if (!player) {
                    this.popupContainerSerivce.createContainer(channelId);
                    return Observable.create(observer => {
                        observer.next("done");
                        observer.complete();
                    });
                }

                const index =  this.camerasAlreadyOpen.indexOf(channelId);
                if (index > -1) {
                    this.camerasAlreadyOpen.splice(index, 1);
                }

                this.openChannelPlaybackAPISubject.next({channelId, speed, time, playerId});
                return Observable.create(observer => {
                    observer.next("done");
                    observer.complete();
                });
            }
         }
         else {
            this.messageService.add({severity: 'error', summary: this._i18n.instant(ToastTypes.error), detail: this._i18n.instant('cameraAlreadyInUse')});
            return Observable.create(observer => {
                observer.next("cameraAlreadyInUse");
                observer.complete();
            });
         }
    }




    public openAllChannelPlayBack(speed: number, time: Date, players: PlayerComponent[]): Observable<void> {

        const resultObserver = Observable.create(observer => {

            let observables = [];
            players.forEach(playerElement => {
                observables.push(playerElement.openChannelPlaybackAPI(playerElement.channelId, speed, time));
            });

            observableForkJoin(observables)
                .subscribe(() => {
                    observer.next();
                    observer.complete();
                });
        });

        return resultObserver;
    }

    public openChannel(id: string, playerId: string = null,isChannelTour?:boolean): void {
        let activePlayer = this.getAvailablePlayer(playerId);
        if (activePlayer) {
            activePlayer.showSpinner = true;
            if (!playerId && activePlayer.channelId === id) {
                setTimeout(() => {
                    activePlayer.showSpinner = false;
                }, 2000);
                return;
            }
            this.openChannelSubject.next({id,isChannelTour,playerId})
        }
        else if (!activePlayer || this._playerCount === 0) {
            this.popupContainerSerivce.createContainer(id);
        }

    }

    public openChannelAPI(id: string): Observable<webrtcData>{
        return this.dataService.api({
            type: ApiCommands.OpenChannel,
            urlParams: id,
            disableErrorHandler: true,
            disableBI: true
        }).pipe(timeout(12000),
            take(1));
    }

    public getActivePlayers(): PlayerModel[] {
        return this._players.filter(player => { return player.isActive;});
    }

    getAvailablePlayer(playerId: string = null): PlayerModel {
        if (playerId) {
            return this.findPlayer(playerId);
        }
        else {
            for (let i = 0; i < this._players.length; i++) {
                if (!this._players[i].isActive && !this._players[i].showSpinner) {
                    return this._players[i];
                }
            }
            for (let i = 0; i < this._players.length; i++) {
                if (!this._players[i].showSpinner) {
                    return this._players[i];
                }
            }
            return null;
        }
    }

    backToLive(playerId: string): void {
        let player = this.findPlayer(playerId);
        if (player) {
            this.setBackToLiveSubject.next(player);
        }
    }


    onDestroy(): void {
        this._players = [];
        this.camerasAlreadyOpen = [];
    }

    public setDragAndDropChannel(channelId: string): void {
        this.draggedChannelData.next(channelId);
    }

    channelIsOpened(channel: string): boolean {
        if(this.camerasAlreadyOpen.includes(channel)){
            return true;
         }else {
            this.camerasAlreadyOpen.push(channel);
            return false;
        }
    }

    removePlayer(channel: string): void {
        this.camerasAlreadyOpen = this.camerasAlreadyOpen.filter(item => { return item !== channel;});
    }
}
