import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { ResourcegroupService } from "app/services/resource/resourcegroup.service";
import { ApiCommands } from "app/shared/enum/enum";
import { Guid } from 'app/shared/enum/guid';
import { ResourceKey } from 'app/shared/models/ResourceKey.model';
import { DeviceInfo } from "app/shared/models/device-info";
import { DimNotification } from 'app/shared/models/dimnotification.interface';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { ResourceState } from "app/shared/modules/data-layer/enum/resource/resource-state.enum";
import { StringToResourceType } from 'app/shared/modules/data-layer/enum/resource/string-to-resource-type.enum';
import { IoResourceModel } from "app/shared/modules/data-layer/models/IoResource.model";
import { ResourceExtraData } from "app/shared/modules/data-layer/models/ResourceExtradata.model";
import { ActionResult } from 'app/shared/modules/data-layer/models/action-result';
import { ApiRequestName } from 'app/shared/modules/data-layer/models/api-request-name.enum';
import { DataChangeNotification } from 'app/shared/modules/data-layer/models/data-change-notification';
import { DataChangeType } from 'app/shared/modules/data-layer/models/data-change-type.enum';
import { DeviceStatus } from "app/shared/modules/data-layer/models/device-status";
import { EditableField } from 'app/shared/modules/data-layer/models/editable-field';
import { Entity } from "app/shared/modules/data-layer/models/entity";
import { FilterOptions } from 'app/shared/modules/data-layer/models/filterOptions.interface';
import { GroupPayloadType } from 'app/shared/modules/data-layer/models/group-payload-type.enum';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import { ExtraData } from "app/shared/modules/data-layer/models/resource-properties.interface";
import { ResourceStateInfo } from 'app/shared/modules/data-layer/models/resource-state-info';
import { ResourceTriggerGroup } from "app/shared/modules/data-layer/models/resource-trigger-group";
import { ResourceLocation } from "app/shared/modules/data-layer/models/resourceLocation.model";
import { ResourceUpdateModel } from "app/shared/modules/data-layer/models/resourceupdate.mode";
import { RtspModel } from 'app/shared/modules/data-layer/models/rtsp.model';
import { DataFormaterService } from 'app/shared/modules/data-layer/services/data-formater/data-formater.service';
import { ResourceDTO } from 'app/shared/modules/data-layer/services/resource/resource-dto';
import { ResourceCacheService } from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import { apiMap } from 'app/shared/services/api.map';
import { SignalRService } from 'app/shared/services/signalR.service';
import { environment } from 'environments/environment';
import { ISignalRConnection } from 'ng2-signalr';
import { Observable, Subject, forkJoin, of } from 'rxjs';
import { ReplaySubject } from 'rxjs/internal/ReplaySubject';
import { map } from 'rxjs/operators';
import { ResourceGroupDTO } from '../../shared/modules/data-layer/services/resource-group/resource-group-dto';


@Injectable({
  providedIn: 'root'
})
export class ResourceService {
  private editableFields:  { [id: number] : EditableField } = {}
    public resourceIOChangedSubject=new Subject<{type: number, models: IoResourceModel}>()
    protected externalDataChangeNotification = new ReplaySubject<DataChangeNotification<Resource>>(1);
    public resourceChanged = new ReplaySubject<DataChangeNotification<Resource>>(1);
    dimLightValue=new Subject<DimNotification>();
    public selectAllLightsSubject=new Subject<{resource:Resource,status:number}>();
    private cachedFilteredData: Map<string, Resource[]> = new Map<string, Resource[]>();
  constructor(private httpClient: HttpClient, private signalRService: SignalRService,private dataFormaterService: DataFormaterService,
  private resourceGroupService:ResourcegroupService,   private resourceCacheService: ResourceCacheService,) { }


  public resetResource(resourceId: Guid):Observable<{}>{
      return this.httpClient.post(environment.apiUrl+apiMap.Reset.url,{type:ApiCommands.Reset,data:{"SensorID":resourceId.toString()}});
  }

  public refreshData(userId: string, resourceId: string): Observable<ActionResult>{
    return new Observable<ActionResult>(observer => {
      this.signalRService.GetConnection('MapHub').subscribe((connection: ISignalRConnection) => {
        if(!connection) {
          console.error('Cannot get reference to signalR connection');
          observer.error('Cannot get reference to signalR connection');
          observer.complete();
          return;
        }
        connection.invoke('RefreshProps', userId, resourceId).then((res: ActionResult) => {
          observer.next(res);
          observer.complete();
        }, (error) => {
          observer.error(error);
          observer.complete();
        });
      });
    });

  }

  //TODO move to lightSaervice
  public dimLight(userId: string, lightLevel: string, resourceId:string, isGroup: boolean): Observable<ActionResult>{
    return new Observable<ActionResult>(observer => {
      this.signalRService.GetConnection('MapHub').subscribe((connection: ISignalRConnection) => {
        if(!connection) {
          console.error('Cannot get reference to signalR connection');
          observer.error('Cannot get reference to signalR connection');
          observer.complete();
          return;
        }
        connection.invoke('SetLight', userId, lightLevel, resourceId, isGroup).then((res: ActionResult) => {
          observer.next(res);
          observer.complete();
        }, (error) => {
          observer.error(error);
          observer.complete();
        });
      });
    });

  }

  public getEditableFields(resourceType: ServerTypes): Observable<EditableField>{

    if(!this.editableFields[resourceType]){
      return new Observable<EditableField>(observer => {
        this.httpClient.get(environment.apiUrl+apiMap.getResourceTypeEDField.url).subscribe((response) => {
          let formattedFields = this.dataFormaterService.formatEditableFields(response);
          formattedFields.forEach((element) => {
              this.editableFields[element.serverType] = element;
           });
           observer.next(this.editableFields[resourceType]);
           observer.complete();
         }, () => {
           observer.error();
           observer.complete();
         });

      });
    }
    return of(this.editableFields[resourceType]);
  }


    private updateResourceFormatData(resource:Resource):ResourceUpdateModel{
        let data = {
            ResourceIdentity: resource.identity,
            ResourceType: resource.resourceType,
            Name: resource.name,
            Lat: resource.location && resource.location.lat ? resource.location.lat : null,
            Lng: resource.location && resource.location.lng ? resource.location.lng : null,
            ExtraData: '[]'
        };
        if(resource.extraData && resource.extraData.length > 0) {
            let properties: {field: string, value: string}[] = [];
            resource.extraData.forEach(el => {
                properties.push({
                    field: el.name,
                    value: el.value
                });
            });
            data.ExtraData = JSON.stringify(properties);
        }
        return data;
    }

  public createRTSP(rtsp: RtspModel[]): Observable<RtspModel> {
    //TODO why do we receive an array of rtsp model and return an observable of one instance

   return new Observable<RtspModel>(observer => {
    let data = [];
    rtsp.forEach((item) => {
      data.push({
        AssociatedRG: item.resourceGroups ?  item.resourceGroups.map(item => { return item.name;}) : "",
        name: item.name,
        ipAddress: item.ipAddress,
        httpPort: item.httpPort,
        rtspLink: item.rtspLink,
        rtspPort: item.rtspPort,
        username: item.username,
        password: item.password,
      });
    });
    this.httpClient.post(environment.apiUrl+apiMap.addRTSPDevice.url,{data:data}).subscribe((response:any) => {
      observer.next(response);
      observer.complete();
    }, (e) => {

      observer.error(e);
      observer.complete();
    });
  });

}


  public deleteResources(resources: ResourceKey[]): Observable<any> {

    return new Observable<Resource>(observer => {
      this.httpClient.post(environment.apiUrl+apiMap.deleteResource.url,{data:resources}).subscribe((res:Resource) => {
        observer.next(res);
        observer.complete();
      }, (e) => {
        observer.error(e);
        observer.complete();
      });
  });

   }


    public getAllStatuses(options?: FilterOptions): Observable<ResourceStateInfo[]> {
        return this.httpClient.get(environment.apiUrl +"/"+ apiMap.getStatuses.url)
            .pipe(map((statuses: Map<string,{state:string}>) => {
                let resourceStateInfos=this.resourceStateInfos(statuses);
                return resourceStateInfos;
            }));
    }

    private resourceStateInfos(statuses: Map<string,{state:string}>) : ResourceStateInfo[]{

        let resourceIdsAsStrings = Object.keys(statuses);
        let resourceStateInfos = resourceIdsAsStrings.map(idAsString =>
        {
            let resourceId = Guid.parse(idAsString);
            let state: ResourceState = statuses[idAsString].state || ResourceState.unknown;
            let strength: number = statuses[idAsString].strength || 0;

            return new ResourceStateInfo(resourceId, state, strength);
        });

        return resourceStateInfos
    }



    public getAll(options?: FilterOptions): Observable<Resource[]> {

        let key = options ? JSON.stringify(options) : "resourceCache";

        if (this.cachedFilteredData.has(key))
        {
            let result = new Observable<Resource[]>(observer => {
                let resources = this.cachedFilteredData.get(key);
                observer.next(resources);
                observer.complete();
            })
            return result;
        }

        let getAllResourceGroups:Observable<ResourceGroup[]> = this.httpClient.get<ResourceGroup[]>(environment.apiUrl +"/"+ apiMap.getUserResourceGroups.url);
        let getAllLocations:Observable<Location[]> = this.httpClient.get<Location[]>(environment.apiUrl +"/"+  apiMap.getLocations.url);
        let getAllExtraData:Observable<ExtraData[]> = this.httpClient.get<ExtraData[]>(environment.apiUrl +"/"+  apiMap.getResourceExtraData.url);
        let getEntityDevices:Observable<Entity[]> = this.httpClient.get<Entity[]>(environment.apiUrl +"/"+  apiMap.entityDevices.url);
        let getDeviceInfo:Observable<DeviceInfo[]> = this.httpClient.get<DeviceInfo[]>(environment.apiUrl +"/"+  apiMap.getDeviceInfo.url);

        return forkJoin([getAllResourceGroups, getAllLocations, getAllExtraData, getEntityDevices, getDeviceInfo])
            .pipe(map((res:any) =>
                {//map operator executes for every subscriber, looks like a bug in forkjoin.js

                    let resourceGroups:ResourceGroup[] = res[0];
                    let locations:ResourceLocation[] = res[1];
                    let extraData:ResourceExtraData[] = res[2];
                    let entityDevices:Entity[] = res[3];
                    let deviceInfo:DeviceInfo[] = res[4];

                    let filteredResources = this.formatDataGetAll(resourceGroups,locations,extraData,entityDevices,deviceInfo, options);
                    this.cachedFilteredData.set(key, filteredResources);
                    return filteredResources;
                })
            );

    }






private formatLocation(data: any[]): Resource[] {
    return data.map(element => {
        const lastURIComponent = element.ResourceURI[element.ResourceURI.length - 1];
        return new Resource({
            identity: element.Identity,
            Guid: element.Identity,
            location: {
                lat: element.Latitude,
                lng: element.Longitude
            },

            resourceType: lastURIComponent ? lastURIComponent : null,
            name: lastURIComponent.name ?  lastURIComponent.name : 'Unknown'
        });
    });
}

private returnResourcesFromResourceGroup(data: ResourceGroup[]): Resource[] {
        const returnedResources: Resource[] = [];

        if (data) {
            data.forEach(group => {
                group.resources.forEach((resource:Resource) => {

                    const deviceIndex = returnedResources.findIndex(el => el.identity === resource.identity);
                    if (deviceIndex === -1) {
                        returnedResources.push(new Resource({
                            identity: resource.identity,
                            Guid: resource.identity,
                            name: resource.name,
                            resourceType: resource.resourceType,
                            groups: [group]
                        }));
                    } else {
                        returnedResources[deviceIndex].groups.push(group);
                    }
                });
            });
        }

        return returnedResources;
    }

    private  formatExtraData(data: any[]): Resource[] {
        const resourcesExtraData: Resource[] = [];

        for (const element of data) {
            try {
                const extraData = JSON.parse(element.ExtraData);

                const resource:Resource = new Resource({
                    identity: element.ResourceIdentity,
                    Guid: element.ResourceIdentity,
                    extraData: []
                });

                for (const item of extraData) {
                    const entry = Object.entries(item)[0];
                    if (entry) {
                        const [objKey, objValue] = entry;

                        if (objKey && typeof objKey === 'string' && objValue !== undefined) {
                            resource.extraData.push({
                                name: objKey,
                                value: objValue as string
                            });
                        }
                    }
                }

                resourcesExtraData.push(resource);
            } catch (err) {
                console.error(err, element);
            }
        }

        return resourcesExtraData;
    }


public generateResourceGroupDTO(data){
  return JSON.parse(data).map(element => {
     return new ResourceGroupDTO(element).toModel();
   });
 }



public onResourceChanged(type: DataChangeType, data: string, requestName?: ApiRequestName): void {

        let models: Resource[]; let resourceGroups: ResourceGroup[];
        switch(requestName){
            case ApiRequestName.getStatuses:
                models = this.formatResourceState(JSON.parse(data)).map((item: DeviceStatus) => {

                    return new Resource({
                        identity: item.identity,
                        Guid: item.identity,
                        status: item.status
                    });
                });
                this.resourceChanged.next({type: type, models: models});
                break;
            case ApiRequestName.getLocations:
                models = this.formatLocation(JSON.parse(data));

                this.resourceChanged.next({type: type, models: models});
                break;
            case ApiRequestName.getResourceGroups:
                models = this.returnResourcesFromResourceGroup(JSON.parse(data));
                this.resourceChanged.next({type: type, models:  models, updateCacheOnly: true});
                break;
            case ApiRequestName.getResourceGroup:
                resourceGroups = this.getResources(data);

                if(resourceGroups[0].groupPayloadType == GroupPayloadType.ResourceAdded){
                    type=DataChangeType.Create;
                }
                if(resourceGroups[0].groupPayloadType == GroupPayloadType.ResourcesRemoved){
                    type=DataChangeType.Delete;
                }
                if(resourceGroups[0].groupPayloadType == GroupPayloadType.AllResources){
                    type=DataChangeType.Update;
                }

                models = this.returnResourcesFromResourceGroup(resourceGroups);

                let resource = this.resourceCacheService.get(models[0].identity);



                this.resourceChanged.next({type: type, models: models, updateCacheOnly: true});
                break;
            case ApiRequestName.getExtraData:
                models = this.formatExtraData(JSON.parse(data));
                this.resourceChanged.next({type: type, models: models});
                break;
            case ApiRequestName.mapHubUpdateElement:
                models = this.formatUpdateElement(JSON.parse(data));
                this.resourceChanged.next({type: type, models: models});
                break;
            case ApiRequestName.getResources:

                this.resourceChanged.next({type: type, models:this.generateResourceDTO(data)});
                break;
            case ApiRequestName.getResource:
                let resourceDto=new  ResourceDTO(JSON.parse(data));
                this.resourceChanged.next({type: type, models: [resourceDto.toModel()]});

                break;
            default:
                break;
        }
    }

    private formatUpdateElement(data: any[]): Resource[] {
        return data.map(element => new Resource({
            identity: element.Id,
            Guid: element.Id,
            location: {
                lat: element.Lat,
                lng: element.Lon
            },
            status: element.state
        }));
    }
     public generateResourceDTO(data){
       let models= JSON.parse(data).map(element => {
          return new ResourceDTO(element).toModel();
        });
        return models;
      }

    private formatResourceState(data: any): DeviceStatus[] {
            return Object.keys(data).map(key => ({
                identity: key,
                status: data[key].State,
            }));
        }
    formatResourceStateMethod(data: any): DeviceStatus[] {
            return this.formatResourceState(data);
        }

    public getResources(data){
        return  [new ResourceGroupDTO(JSON.parse(data)).toModel()]
       }


   public delete(model: Resource): Observable<Resource> {
        return this.httpClient.post(environment.apiUrl+apiMap.deleteResource.url+"/"+model.identity,{}).pipe(map((response:Resource) => {
          return this.decode(response);
        }));
    }

    public decode(dto: Resource): Resource {
      return new Resource(dto);
    }


    public update(resource: Resource): Observable<Resource> {
      return new Observable<Resource>(observer => {
        let data = {
          ResourceIdentity: resource.identity,
          ResourceType: resource.resourceType,
          Name: resource.name,
          Lat: resource.location.lat ? resource.location.lat : null,
          Long: resource.location.lng ? resource.location.lng : null,
          ExtraData: '[]'
        };
        if(resource.extraData && resource.extraData.length > 0) {
          let properties: {field: string, value: string}[] = [];
          resource.extraData.forEach(el => {
            properties.push({
              field: el.name,
              value: el.value
            });
          });
          data.ExtraData = JSON.stringify(properties);
        }
        this.httpClient.post(environment.apiUrl+apiMap.setInventoryEntry.url,data).subscribe(() => {
          observer.next();
          observer.complete();
        }, (e) => {
          observer.error(e);
          observer.complete();
        });
      });

    }
    public resourceChangedMethod(): Observable<DataChangeNotification<any>>{
      return this.externalDataChangeNotification;
    }

//TODO make this private, refactor the formatOptions parameter type, let's prove we can CODE!
    public  formatResource(data: Resource, group: ResourceGroup, formatOptions?: { status: string, strength:number, location: {Latitude: number, Longitude: number},
        extraData?: { [extraDataName: string] : string }[], providesIdentification?: boolean, isCortica: boolean}): Resource {

        return new Resource({
            identity: data.Guid,
            Guid:data.Guid,
            name: data.name,
            resourceType: data.resourceType,
            extraData: formatOptions?.extraData?.map((el) => ({
                name: Object.keys(el)[0],
                value: el[Object.keys(el)[0]]
            })) ?? [],
            groups: [group],
            status: formatOptions ? ResourceState[formatOptions.status] : ResourceState.unknown,
            strength: formatOptions?.strength ?? 0,
            location: {
                lat: formatOptions?.location?.Latitude ?? null,
                lng: formatOptions?.location?.Longitude ?? null
            },
            providesIdentification: formatOptions?.providesIdentification ?? false, // Optional default
            isCortica: formatOptions?.isCortica ?? false, // Optional default
            URI: data.URI
        });
    }

    private  formatResourceTrigger(data: any, guid?): ResourceTriggerGroup {
        return {
            identity: guid || Guid.EMPTY,
            Triggers: Object.keys(data).map(key => ({
                Metadata: data[key].Metadata,
                Name: key,
                SurpriseCode: data[key].SurpriseCode
            }))
        };
    }

    private  formatResourceGroup(data: any[], locations: any[]): ResourceGroup[] {
        const locationMap = new Map(locations.map(location => [location.Identity, location]));

        return data.map(element => {
            const location = locationMap.get(element.Identity);
            return new ResourceGroup({
                identity: element.Identity,
                isHomogeneous: element.IsHomogeneousRG,
                name: element.Name,
                allResourcesType: element.ResourceType,
                resources: element.Resources.map(res => this.formatResources(res)),
                location: location ? { lat: location.Latitude, lng: location.Longitude } : { lat: null, lng: null }
            });
        });
    }

    private  formatResources(data: any): Resource {
        return new Resource({
            identity: data.id,
            Guid: data.id,
            name: data.name,
            resourceType: data.type,
            status: data.status || null
        });
    }



    private formatDataGetAll(resourceGroupsParam: ResourceGroup[],locationsParam:ResourceLocation[],extraDataParam:ResourceExtraData[],
                             entityDevicesParam:Entity[],deviceInfoParam:DeviceInfo[] ,options?: FilterOptions): Resource[] {

        let filteredData: Map<string, Resource> = new Map<string, Resource>();
        let resourceGroups = this.resourceGroupService.formatResourceGroups(resourceGroupsParam, extraDataParam);

        let locations = locationsParam;
        let extraData = extraDataParam;
        let entityDevices = entityDevicesParam;
        let deviceInfo = deviceInfoParam;
        const extradataMap = Object.assign({}, ...extraData.map(s => ({[s.ResourceIdentity.toString()]: s})));

        const deviceInfoMap = Object.assign({}, ...deviceInfo.map(s => ({[s.id.toString()]: s})));
        resourceGroups.forEach(group => {
            group.resources.forEach(resource => {
                let existedResource = filteredData.get(resource.identity);
                if(!existedResource){
                    let filtered:boolean = true;

                    let formatOptions: {status: string, strength:number,  location: {Latitude: number, Longitude: number},
                        extraData: { [extraDataName: string] : string }[], providesIdentification: boolean, isCortica: boolean} = {
                        status: ResourceState.unknown,
                        strength: 0,
                        location: locations.find(el => {return el.Identity === resource.identity;}),
                        extraData: extradataMap[resource.identity] != null ? JSON.parse(extradataMap[resource.identity].ExtraData) : null,
                        providesIdentification:  entityDevices.findIndex(entity => {return entity.identity === resource.identity;}) > -1 ? true : false,
                        isCortica: deviceInfoMap[resource.identity] != null && (deviceInfoMap[resource.identity].spec  && deviceInfoMap[resource.identity].spec !== null)
                        && deviceInfoMap[resource.identity].spec.TypeId === StringToResourceType.CORTICA ? true : false,
                    };

                    let formatedReducer: Resource = this.formatResource(resource, group, formatOptions);

                    if(options && options.filters){
                        options.filters.forEach(filter => {
                            let filterKey = Object.keys(filter)[0];
                            if(formatedReducer[filterKey] !== parseInt(filter[filterKey])){
                                filtered = false;
                            }
                        });
                    }

                    if(filtered){
                        if(options && options.properties){
                            let partial: Resource = new Resource({
                                identity: formatedReducer.identity,
                                Guid: formatedReducer.identity
                            });
                            options.properties.forEach(property => {
                                partial[property] = resource[property];
                            });
                            filteredData.set(partial.identity, partial);
                        }
                        else {
                            filteredData.set(formatedReducer.identity, formatedReducer);
                        }
                    }
                }else{
                    existedResource.groups.push(group);
                }

            });
        });

        let filteredDataArray: Resource[] = [];
        filteredData.forEach((value) => {
            filteredDataArray.push(value);
        });


        return filteredDataArray;

    }



}
