import {Component,Input} from '@angular/core';
import { TableActionsComponent } from '../../../shared/components/ng-turbo-table/models/table-actions-component';
import {PlayersService} from "../../../shared/services/players.service";
import {NavigationService,Pages} from "../../../shared/services/navigation.service";
import { GeneralDetectionService } from '../../../services/generalDetections/general-detection.service';
import { GeneralDetection } from '../../../shared/modules/data-layer/models/general-detections/general-detection';

@Component({
  selector: 'app-general-detection-actions',
  templateUrl: './general-detection-actions.component.html',
  styleUrls: ['./general-detection-actions.component.scss']
})
export class GeneralDetectionActionsComponent extends TableActionsComponent {
    @Input('row') row: GeneralDetection;

    tooltipOptions = {
      showDelay: 150,
      autoHide: false,
      tooltipEvent: 'hover',
      tooltipPosition: 'top'
    };

    constructor(private navigationService: NavigationService,
      private playersService: PlayersService,
      private generalDetectionService: GeneralDetectionService)
        { super() }

        playback(): void {
          this.playersService.openChannelPlayback(this.row.inputChannel, 1, this.row.timeStamp, null).subscribe();
      }

      jumpToMap(): void {
          this.navigationService.navigate(Pages.mapNew, { identity: this.row.inputChannel });

          //  this.navigationService.navigate(Pages.mapNew,{
          //           zoom:19,
          //           identity: action.Resource,
          //       });
      }

    jumpToProcedures(): void {
        this.navigationService.navigate(Pages.procedures, { eventId: this.row.id });
    }

      followOnTrafficDetections(): void {
          this.navigationService.navigate(Pages.swarmLPR, { entityId: this.row.entityId });
      }

      downloadEventArchive(): void {
        this.generalDetectionService.downloadEventArchive(this.row.id.toString()).subscribe({
            next: (response) => {

              const blobUrl = window.URL.createObjectURL(response.data);
              const timeStamp = this.row.timeStamp.toString().replace(/\//g, "_").replace(", ", "_").replace(" ", "_");

              const a = document.createElement('a');
              a.href = blobUrl;
              a.download = "EventArchive-" + this.row.id + "-" + timeStamp + ".zip";
              a.style.display = 'none';

              document.body.appendChild(a);
              a.click();

              document.body.removeChild(a);
              window.URL.revokeObjectURL(blobUrl);
            },
            error: (error) => console.error("Download event archive failed", error),
          });
    }

    jumpToTrafficDetections(): void {
      let plateValue : string = this.row.entityId;
      let itemId : string = this.row.foreignId;

      this.navigationService.navigate(Pages.vehicleTraffic,  { plateNumber: plateValue, itemId: itemId, search: plateValue, pageIndex: 0 });
  }

  jumpToDossier(): void {
    let eventId : string = this.row.id;
    this.navigationService.navigate(Pages.dossier,  { eventId });
  }
}
