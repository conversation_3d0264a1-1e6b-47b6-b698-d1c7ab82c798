<div class="map-actions-wrapper">
  
  <div class="actions">
    <ng-container *ngFor="let action of editActions">
      <button
        *ngIf="action.isVisible"  
        class="btn btn-link"
        pTooltip="{{ action.name | translate }}" 
        [ngClass]="{'selected': action.isActive }" 
        (click)="emitMapAction(action.type)">
        <i class="menu-icon {{action.iconClass}}"></i>
      </button>
    </ng-container>  
    
    
    <app-fullscreen [element]="mapSelector" [addPopUps]="true" #fullScreen pTooltip="{{ 'fullScreen' | translate }}"></app-fullscreen>

    <!-- Commented out for now. For MMAP is not needed -->
    <!-- <button class="btn btn-link" (click)="overlayActions.toggle($event)" pTooltip="{{ 'actions' | translate }}"><i class="fa fa-ellipsis-v"></i></button>
    <p-overlayPanel appendTo="body" #overlayActions>
      <ul class="actions-menu">
        <li *ngFor="let action of dropDownActions">
          <a href="javascript:void(0)" *ngIf="action.isVisible" (click)="emitMapAction(action.type)" title="{{action.name | translate}}">{{action.name | translate}}</a>
        </li>
      </ul>
    </p-overlayPanel> -->
  </div>  
</div>
