import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';

import { Guid } from 'app/shared/enum/guid';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { URI } from 'app/shared/enum/uri';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { ResourceGroup } from "app/shared/modules/data-layer/models/resource-group";
import { ResourcePort } from "app/shared/modules/data-layer/services/resource/resource-port";
import { ResourceCacheService } from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import { DataService } from 'app/shared/services/data.service';
import { Export2CSV } from 'app/shared/services/exportData.service';
import * as _ from 'lodash';
import { MessageService } from 'primeng/api';
import { Subscription } from 'rxjs';
import { first, map } from "rxjs/operators";
import * as fromAppReducers from '../../../store/app.reducers';
import { ConfigUploaderComponent } from './../../../shared/components/config-uploader/config-uploader.component';
import {ResourceGroupService} from "app/shared/services/resourceGroup.service";
import {ApiCommands} from "app/shared/enum/enum";
import moment from 'moment';

@Component({
    selector: 'app-entity-uploader',
    templateUrl: './entityUploader.component.html',
    styleUrls: ['./entityUploader.component.scss']
})
export class EntityUploaderComponent implements OnInit, OnDestroy {
    @ViewChild('configUploader', {static:false}) private configUploader: ConfigUploaderComponent;
    @ViewChild('summaryModal', {static:false}) summaryModal: AppModal;

    serverAssociatedDevices = [];
    serverResourceGroups = [];
    transactionIdMap = new Map<number, any>();
    @Input('Entities') selectedEntities: any[] = [];

    entityKeys = [
        { id: 'key_image', text: 'image', data: 'Img' },
        { id: 'key_firstName', text: 'firstName', data: 'FirstName', required: true },
        { id: 'key_lastName', text: 'lastName', data: 'LastName', required: true },
        { id: 'key_username', text: 'username', data: 'Username' },
        { id: 'key_identity', text: 'identity', data: 'Identity' },
        { id: 'key_startDate', text: 'startDate', data: 'StartDate' },
        { id: 'key_endDate', text: 'endDate', data: 'EndDate' },
        { id: 'key_mobilePhone', text: 'mobilePhone', data: 'MobilePhone' },
        { id: 'key_email', text: 'email', data: 'Email' }
    ];

    serverKeysConfig = [];

    totalEntitiesAdded = 0;
    totalEntitiesUpdated = 0;
    totalEntitiesFailed = 0;
    totalEntitiesMissingMandatoryFields = 0;
    uploadedElementsSummary = [];
    lastTransId = null;
    subscriptions: Subscription[] = [];

    constructor(private dataService: DataService,
                private export2CSV: Export2CSV,
                private store: Store<fromAppReducers.AppState>,
                private i18n: TranslateService,
                private messageService: MessageService,
                private resourceGroupService:ResourceGroupService,
    ) {
    }

    ngOnInit(): void {
        const entityDeviceSubscription = this.dataService.api({
            type: ApiCommands.EntityDevices,
            disableErrorHandler: true,
            disableBI: true
        }).subscribe(devices => {
            const resourceGroupsSubscription = this.resourceGroupService.getAll().subscribe(groups => {
                this.serverResourceGroups = groups.map(group => ({
                    id: `group_${group.name}`,
                    text: group.name,
                    data: group
                }));

                if (Array.isArray(devices)) {
                    this.serverAssociatedDevices = devices.map(device => ({
                        id: `device_${new URI(device).endName}`,
                        text: new URI(device).endName,
                        data: new URI(device)
                    }));
                }

                this.serverKeysConfig = [
                    {
                        groupName: 'keys',
                        groupKeys: this.entityKeys
                    },
                    {
                        groupName: 'resourceGroups',
                        groupKeys: [
                            { id: 'addNewResourceGroup', text: 'addNewResourceGroup', data: 'addNewResourceGroup' },
                            ...this.serverResourceGroups
                        ]
                    },
                    {
                        groupName: 'associatedDevices',
                        groupKeys: this.serverAssociatedDevices
                    },
                    {
                        groupName: 'dynamicAttributes',
                        groupKeys: [{ id: 'dynamicAttributes', text: 'dynamicAttributes', data: 'dynamicAttributes', type: this.configUploader.Types.Array }]
                    }
                ];
            });

            this.subscriptions.push(resourceGroupsSubscription);
        });

        this.subscriptions.push(entityDeviceSubscription);
    }

    onSummaryConfirm() {
        if (this.lastTransId) {
           let deleteEntitySubscription= this.dataService.api({
                type: ApiCommands.DeleteUploadEntitiesResults,
                urlParams: this.lastTransId
            }).subscribe(() => {
                this.lastTransId = null;
            });
            this.subscriptions.push(deleteEntitySubscription);
        }
        this.summaryModal.closeModal();
    }

    DownloadTemplate() {
       let getEntitiesSubscription= this.dataService.api({
            type: ApiCommands.GetEntities,
            disableErrorHandler: true,
        })
        .pipe(map((res:any[]) => {
            res.forEach(entity => {
                entity.startDate = moment().utc(entity.startDate).local().toDate();
                entity.endDate = moment().utc(entity.endDate).local().toDate();
            });

            return res;
        }))
        .subscribe(res => {
            let serverEntities = res || [];

            let entitiesToDownload = [{
                Identity: "",
                Img: "",
                FirstName: "",
                LastName: "",
                Username: "",
                MobilePhone: "",
                StartDate: "",
                EndDate: "",
                Email: ""
            }];

            this.serverResourceGroups.forEach(group => {
                entitiesToDownload[0][group.data.Name] = "";
            });

            this.serverAssociatedDevices.forEach(device => {
                entitiesToDownload[0][device.text] = "";
            });

            this.selectedEntities = this.selectedEntities.filter(e => e.typeId == ServerTypes.Core_RES_Entity);
            //get unique values
            this.selectedEntities = _.uniqBy(this.selectedEntities, "id");
            this.selectedEntities.forEach(selectedEntity => {
                let serverEntity = serverEntities.find(ent => ent.Identity == selectedEntity.id);
                if (!serverEntity) {
                    return;
                }

                let entity = {
                    Identity: serverEntity.Identity,
                    Img: serverEntity.Img,
                    FirstName: serverEntity.FirstName,
                    LastName: serverEntity.LastName,
                    Username: serverEntity.Username,
                    MobilePhone: serverEntity.MobilePhone,
                    StartDate: new Date(serverEntity.StartDate).toISOString(),
                    EndDate: new Date(serverEntity.EndDate).toISOString(),
                    Email: serverEntity.Email
                };

                this.serverResourceGroups.filter(group => group.data.Resources.findIndex(resource => resource.endId == entity.Identity) != -1).forEach(group => {
                    entity[group.Name] = true;
                });

                serverEntity.AssociatedDevices.forEach(device => {
                    entity[device.DeviceName] = device.Value;
                });

                entitiesToDownload.push(entity);
            });

            this.export2CSV.downloadFromJson(entitiesToDownload, 'entitiesTemplate');
        });
        this.subscriptions.push(getEntitiesSubscription);
    }

    UploadFile() {
        this.configUploader.OpenDialog();
    }

    onDataUploaded(evt) {
        let entitiesToUpload = [];
        this.totalEntitiesMissingMandatoryFields = 0;
        this.uploadedElementsSummary = [];

        evt.data.forEach(element => {
            let newEntity = {
                AssociatedDevices: [],
                AssociatedRG: [],
                Img: "",
                FirstName: "",
                LastName: "",
                Username: "",
                Email: "",
                Enabled: true,
                MobilePhone: "",
                StartDate: new Date().toISOString(),
                EndDate: new Date().toISOString(),
                DynamicAttributes: {}
            };

            let elementKeys = Object.keys(element);
            for (let i = 0; i < elementKeys.length; i++) {
                let key = elementKeys[i];

                if (key == 'dynamicAttributes') {
                    element.dynamicAttributes.forEach(dynamicAttribute => {
                        Object.assign(newEntity.DynamicAttributes, dynamicAttribute);
                    });
                }
                else if (evt.newResourceGroups.indexOf(key) != -1 && element[key].toLowerCase() == 'true') {
                    let group = this.serverResourceGroups.find(q => q.id == key);
                    if (group) {
                        newEntity.AssociatedRG.push({
                            Name: group.data.Name,
                            Identity: group.data.Identity
                        });
                    } else {
                        newEntity.AssociatedRG.push({
                            Name: key,
                            Identity: Guid.create().toString()
                        });
                    }
                }
                else {
                    let objType = key.split("_")[0];

                    switch (objType) {
                        case 'key': {

                            if (this.containsRequiredKeys(element)) {
                                let entityKey = this.entityKeys.find(q => q.id == key).data;
                                if (entityKey == 'StartDate' || entityKey == 'EndDate') {
                                    let date = Date.parse(element[key]);
                                    if (!isNaN(date)) {
                                        newEntity[entityKey] = new Date(element[key]).toISOString();
                                    }
                                }
                                else {
                                    newEntity[entityKey] = element[key];
                                }
                            }
                            else {
                                this.totalEntitiesMissingMandatoryFields++;

                                let obj = Object.keys(element).reduce((result, key) => {
                                    let entityKey = this.entityKeys.find(q => q.id == key);

                                    let objType = key.split("_")[0];
                                    let objKey = entityKey ? entityKey.data : key.split("_")[1];

                                    switch (objType) {
                                        case 'key': {
                                            result[objKey] = element[key];
                                            break;
                                        }
                                        case 'group': {
                                            result[objKey] = 'TRUE';
                                            break;
                                        }
                                        case 'device': {
                                            result[objKey] = element[key];
                                            break;
                                        }
                                    }

                                    return result;
                                }, {});

                                if (element.dynamicAttributes) {
                                    element.dynamicAttributes.forEach(dynamicAttribute => {
                                        Object.assign(obj, dynamicAttribute);
                                    });
                                }

                                this.uploadedElementsSummary.push({
                                    ...obj,
                                    State: this.i18n.instant("missingMandatoryFields")
                                });

                                return;
                            }
                            break;
                        }
                        case 'group': {
                            let group = this.serverResourceGroups.find(q => q.id == key);
                            if (group && element[key].toLowerCase() == 'true') {
                                newEntity.AssociatedRG.push({
                                    Name: group.data.Name,
                                    Identity: group.data.Identity
                                });
                            }
                            break;
                        }
                        case 'device': {
                            let device = this.serverAssociatedDevices.find(q => q.id == key).data;
                            if (device && element[key])
                                newEntity.AssociatedDevices.push({
                                    DeviceName: device.endName,
                                    Value: element[key].trim(),
                                    DeviceId: device.endId
                                });

                            break;
                        }
                    }
                }
            }

            if (!newEntity['Identity']) {
                newEntity['Identity'] = Guid.create().toString();
            }
            else if (!Guid.isGuid(newEntity['Identity'])) {
                this.totalEntitiesMissingMandatoryFields++;
                this.uploadedElementsSummary.push({
                    ...newEntity,
                    State: this.i18n.instant('wrongGuidFormat')
                });
                return;
            }

            entitiesToUpload.push(newEntity);
        });

        this.uploadEntities(entitiesToUpload);
    }

    containsRequiredKeys(element) {
        return element && element['key_firstName'] && element['key_lastName'];
    }

    uploadEntities(entities) {
        if (!(entities instanceof Array) || entities.length == 0) {
            return;
        }

        let addEntitiesSubscription= this.dataService.api({
            type: ApiCommands.AddEntities,
            disableErrorHandler: true,
            disableBI: true,
            data: entities
        }).subscribe(res => {
                this.messageService.add({severity: 'success', summary: this.i18n.instant(ToastTypes.success), detail: this.i18n.instant('entitiesUploadedSuccessfully')});
                this.totalEntitiesAdded = res.data.EntityAdded;
                this.totalEntitiesUpdated = res.data.EntityUpdated;
                this.totalEntitiesFailed = res.data.EntityFailed;

                this.lastTransId = res.transId;
                this.summaryModal.openModal();
            },
            () => { });
        this.subscriptions.push(addEntitiesSubscription);
    }

    downloadSummaryFile() {
        if (this.lastTransId) {
            let getUploadEntitiesResultSubscription=  this.dataService.api({
                type: ApiCommands.GetUploadEntitiesResults,
                urlParams: this.lastTransId
            })
            .pipe(map((res: any[]) => {
                res?.forEach((element, i) => {
                    element.startDate = moment.utc(element.startDate).local().toDate();
                    element.endDate = moment.utc(element.endDate).local().toDate();
                });

                return res;
            }))
            .subscribe(res => {
                if (res instanceof Array) {
                    res.forEach(entity => {
                        let ent = {
                            Img: entity['Img'],
                            FirstName: entity['FirstName'],
                            LastName: entity['LastName'],
                            Username: entity['Username'],
                            MobilePhone: entity['MobilePhone'],
                            StartDate: new Date(entity['StartDate']).toISOString(),
                            EndDate: new Date(entity['EndDate']).toISOString(),
                            Email: entity['Email'],
                            State: entity['state']
                        };

                        let devices = entity['AssociatedDevices'];
                        if (devices instanceof Array) {
                            devices.forEach(device => {
                                ent[device['DeviceName']] = device['Value'];
                            });
                        }

                        let groups = entity['AssociatedRG'];
                        if (groups instanceof Array) {
                            groups.forEach(group => {
                                ent[group['Name']] = 'TRUE';
                            });
                        }

                        Object.assign(ent, entity.DynamicAttributes);

                        this.uploadedElementsSummary.push(ent);
                    });
                }

                this.export2CSV.downloadFromJson(this.uploadedElementsSummary, 'entities');
            });
            this.subscriptions.push(getUploadEntitiesResultSubscription);
        }
    }

    ngOnDestroy(): void {
        this.subscriptions.forEach(item => item.unsubscribe());
    }
}
