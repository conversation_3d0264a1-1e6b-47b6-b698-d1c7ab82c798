import {
    ChangeDetectorRef,
    Component,
    ComponentFactoryResolver,
    ComponentRef,
    EventEmitter,
    Inject,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
    Type,
    ViewChild,
    ViewContainerRef
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { MapSearchOperand } from 'app/layout/app-map/enums/map-search-operand.enum';
import { MapFilters } from 'app/layout/app-map/models/map-filters.interface';
import { MapSearchProperties } from 'app/layout/app-map/models/map-search-properties.interface';
import { MapService } from 'app/services/map/map.service';
import { ResourceService } from 'app/services/resource/resource.service';
import { Guid } from 'app/shared/enum/guid';
import { MapType } from 'app/shared/modules/data-layer/enum/map/map-type.enum';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { AppNotification } from 'app/shared/modules/data-layer/models/app-notification';
import { DataChangeType } from 'app/shared/modules/data-layer/models/data-change-type.enum';
import { Event } from 'app/shared/modules/data-layer/models/event';
import { Map as MapModel } from 'app/shared/modules/data-layer/models/map';
import { MapElement, MapElementType, TextMapElement } from 'app/shared/modules/data-layer/models/map-elements/map-element';
import { MapLayer } from 'app/shared/modules/data-layer/models/map-layer';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import { EventServiceNew } from 'app/shared/modules/data-layer/services/event/eventServiceNew';
import { MapElementService } from 'app/shared/modules/data-layer/services/map-element/map-element.service';
import { MapLayerService } from 'app/shared/modules/data-layer/services/map-layer/map-layer.service';
import { ResourceGroupService } from 'app/shared/modules/data-layer/services/resource-group/resource-group.service';
import { ResourceCacheService } from 'app/shared/modules/data-layer/services/resource/resource.cache.service';
import { IMapProjectionTransformService } from 'app/shared/modules/generic-map/components/base-map-transform.service';
import { GenericMapComponent } from 'app/shared/modules/generic-map/components/generic-map.component';
import {
    Base64TileSource,
    Point as CYPoint,
    ExtentObj,
    Marker,
    ProjectionEnum,
    ProjectionUnits,
    TileSource
} from 'app/shared/modules/generic-map/models/map.models';
import { AuthService } from 'app/shared/services/auth.service';
import { EventService, EventType } from 'app/shared/services/event.service';
import { SignalRService } from 'app/shared/services/signalR.service';
import * as _ from 'lodash';
import { ISignalRConnection } from 'ng2-signalr';
import { MessageService } from 'primeng/api';
import { Observable, Subscription, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { GisTileProvider } from '../../enums/gis-tile-provider.enum';
import { ResourceData } from '../../models/resource-data.interface';
import { ResourceGroupData } from '../../models/resource-group-data.interface';
import { CymbiotMapUtilsService } from '../../services/cymbiot-map-utils.service';
import { CymbiotPopupService } from '../../services/cymbiot-popup.service';
import { CustomMarkerComponent } from '../custom-marker/custom-marker.component';
import { CustomPopupComponent } from '../custom-popup/custom-popup.component';
import { DefaultMarkerComponent } from '../default-marker/default-marker.component';
import { ResourceGroupMarkerComponent } from '../resource-group-marker/resource-group-marker.component';
import { TextMarkerComponent } from '../text-marker/text-marker.component';
import { CymbiotMarker } from './cymbiot-marker';

@Component({
    selector: 'app-cymbiot-map',
    templateUrl: './cymbiot-map.component.html',
    styleUrls: ['./cymbiot-map.component.scss']
})
export class CymbiotMapComponent implements OnInit, OnChanges, OnDestroy {
    @Input('selectedMapId') selectedMapId: string;
    @Input('mapFilterOptions') mapFilterOptions: MapFilters;
    @Input('mapSearchProperties') mapSearchProperties: MapSearchProperties;
    @Input('defaultItemId') defaultItemId: string;
    @Output('extentChanged') extentChanged: EventEmitter<ExtentObj> = new EventEmitter();
    @ViewChild('customMarkersFactory', { read: ViewContainerRef, static: false }) customMarkersFactory: ViewContainerRef;
    @ViewChild('map', { static: false }) map: GenericMapComponent;
    @Input('mapLayers') mapLayers: Map<string, MapLayer>;

    zoom: number;
    selectedMap: MapModel;
    mapTileSource: TileSource;
    extent: ExtentObj;
    zoomToFitEnabled: boolean;
    mapExtent: ExtentObj;
    private subscriptions: { gisMap: Subscription[], rasterMap: Subscription[], shared: Subscription[] } = {
        gisMap: [],
        rasterMap: [],
        shared: []
    };

    private resourceModelStore: Map<string, Resource> = new Map<string, Resource>();
    private mapElementsModelStore: Map<string, MapElement> = new Map();
    private resourceGroupsModelStore: Map<string, ResourceGroup> = new Map();

    private addEventResourceModelStore: Map<string, Event> = new Map();
    private defaultLatitude: number;
    private defaultLongitude: number;

    markersArray: CymbiotMarker[] = [];
    mapElements: MapElement[] = [];
    notifications: AppNotification[] = [];
    private resources: Resource[];
    mapLayersObservable: Observable<Map<string, MapLayer>>;
    resourceGroupsObservable: Observable<Map<string, ResourceGroup>>;
    customPopupComponentRef: ComponentRef<CustomPopupComponent>;
    markersDictionary: Map<string, CymbiotMarker> = new Map<string, CymbiotMarker>();
    signalRConnection: ISignalRConnection;
    center: CYPoint = null;
    openedMarkerComponent: DefaultMarkerComponent = null;
    instanceId: string = Guid.create().toString();
    isDefault: boolean;
    onLoadMarker: CymbiotMarker;
    routeActivated: boolean;
    currentIndex: number;

    constructor(
        private mapService: MapService,
        private cymbiotMapUtilsService: CymbiotMapUtilsService,
        @Inject('IMapProjectionTransformService') private projectionTransform: IMapProjectionTransformService,
        private resourceService: ResourceService,
        private resourceCacheService: ResourceCacheService,
        private mapElementService: MapElementService,
        private componentFactoryResolver: ComponentFactoryResolver,
        private signalRService: SignalRService,
        private authService: AuthService,
        private eventServiceNew: EventServiceNew,
        private cymbiotPopupService: CymbiotPopupService,
        private activeRouter: ActivatedRoute,
        private eventService: EventService,
        private mapLayerService: MapLayerService,
        private resourceGroupService: ResourceGroupService,
        private cdRef: ChangeDetectorRef,
        private messageService: MessageService,
        private translateService: TranslateService
    ) {
        this.markersDictionary = new Map<string, CymbiotMarker>();
        this.resourceService.resourceChanged.subscribe(resources => {
            resources.models.map(item => {
                if (typeof item === 'string') {
                    let parsedItem = JSON.parse(item);
                    this.updateOrCreateMarkers([parsedItem])
                } else {
                    let resources = [item];

                    this.updateOrCreateMarkersExtradata(resources);
                    this.updateOrCreateMarkers(resources);
                }
            })

            this.forceChangeDetectionOnChildComponents();
        })
        this.cymbiotPopupService.indexChanging.subscribe(index => {
            this.currentIndex = index;
            let marker = this.markersArray[index];

            this.activateMarker(marker.component, marker);
        })
    }

    ngOnInit(): void {
        let addEventDataChangeSubscription = this.eventServiceNew.resourceChanged().subscribe(res => {
            switch (res.type) {
                case DataChangeType.Create:
                    this.updateOrCreateEvents(res.models);
                    break;
                case DataChangeType.Delete:
                    this.deleteEvents(res.models);
                    break;
                default:
                    console.log('resourceChanged() there is no action based on response type', res.type, DataChangeType);
                    break;
            }
        });
        this.subscriptions.shared.push(addEventDataChangeSubscription);

        let activeRouteSubscription = this.activeRouter.queryParams.subscribe((params) => {
            if (params['map']) {
                this.instanceId = params['map'];
                //TODO: to revert this later when MapLayerService will no longer inherit BaseService
                this.showAllMapLayers();
            }
            if (params['dashboard']) {
                this.isDefault = false;
                return;
            }

            if (params["latitude"] && params["longitude"]) {
                this.defaultLatitude = parseFloat(params["latitude"]);
                this.defaultLongitude = parseFloat(params["longitude"]);
                let radius = parseInt(params["radius"]);
                if (radius)
                {
                    this.setZoomFromRadius(radius);
                }
            }

            if (!this.defaultItemId && !this.zoom) {
                this.zoom = params['zoom'];
            }

            this.isDefault = false;
            this.routeActivated = true;
        });
        this.subscriptions.shared.push(activeRouteSubscription);

        let sidebarResizeSubscription = this.eventService.observe(EventType.ViewPortResize).subscribe(() => { return this.updateSize(); });
        this.subscriptions.shared.push(sidebarResizeSubscription);
    }

    ngOnChanges(changes: SimpleChanges): void {

        if (changes && changes.selectedMapId && changes.selectedMapId.currentValue) {
            if (this.signalRConnection) {
                this.signalRConnection.invoke('PageLoaded', this.authService.user.Identity, changes.selectedMapId.currentValue);
            }
            let selectedMapId = changes.selectedMapId.currentValue;
            this.getMapInfo(selectedMapId);
        }
        if (changes && changes.mapFilterOptions && changes.mapFilterOptions.currentValue) {
            this.applyFilters(this.resourceModelStore, this.mapElementsModelStore, this.mapLayers, this.resourceGroupsModelStore, changes.mapFilterOptions.currentValue, this.mapSearchProperties);
            this.forceChangeDetectionOnChildComponents();
            this.activateZoomToFit();
        }
        if (changes && changes.mapSearchProperties && changes.mapSearchProperties.currentValue) {
            this.applyFilters(this.resourceModelStore, this.mapElementsModelStore, this.mapLayers, this.resourceGroupsModelStore, this.mapFilterOptions, changes.mapSearchProperties.currentValue);

            // let searchResultCount = Object.keys(this.markersDictionary).length;
            let searchResultCount = this.markersDictionary.size;

            this.messageService.add({
                severity: 'success', summary: this.translateService.instant('appMap.searchSuccessful'),
                detail: this.translateService.instant('appMap.searchHasResultsNumber', { resultsNumber: searchResultCount }) + (searchResultCount === 0 ? this.translateService.instant('appMap.noResultsSummary') : '')
            });
            if (searchResultCount === 0) {
                return;
            }
            this.forceChangeDetectionOnChildComponents();
            this.activateZoomToFit();
        }
        if (changes && changes.mapLayers && changes.mapLayers.currentValue) {
            this.applyFilters(this.resourceModelStore, this.mapElementsModelStore, changes.mapLayers.currentValue, this.resourceGroupsModelStore, this.mapFilterOptions, this.mapSearchProperties);
            this.forceChangeDetectionOnChildComponents();
            this.activateZoomToFit();
        }

        let mapServiceChangeSubscription = this.mapService.resourceChanged.subscribe(res => {
            res.models.forEach(map => {
                if (map.identity === this.selectedMapId && res.type === DataChangeType.Update) {
                    this.extent = this.cymbiotMapUtilsService.returnExtent(map);
                    this.mapExtent = this.extent;
                }
            });
        });
        this.subscriptions.shared.push(mapServiceChangeSubscription);
    }

    ngOnDestroy(): void {
        for (let key in this.subscriptions) {
            this.subscriptions[key].forEach(subscription => { return subscription.unsubscribe(); });
        }
        this.cymbiotPopupService.clearClusters(this.instanceId);
    }

    private showAllMapLayers() {
        this.mapLayers.forEach((mapLayer: MapLayer) => {
            mapLayer.hidden = false;
        })
    }

    private updateOrCreateMarkers(resources: Resource[]): void {


        let toFilter: Map<string, Resource> = new Map<string, Resource>();

        resources.forEach(resource => {
            if (!resource.location || !resource.location.lat || !resource.location.lng) {
                return;
            }

            this.resourceModelStore.set(resource.identity, resource);
            toFilter.set(resource.identity, resource);
        });

        let filteredMarkers: Map<string, CymbiotMarker> = this.filterResources(toFilter, this.mapElementsModelStore, this.mapLayers, this.resourceGroupsModelStore, this.mapFilterOptions, this.mapSearchProperties);

        filteredMarkers.forEach((marker, key) => {
            this.markersDictionary[key]= marker;
        });

        this.cdRef.detectChanges();
    }

    updateOrCreateMarkersExtradata(resources: Resource[]): void {
        let toFilter: Map<string, Resource> = new Map();
        let lat = null;
        let lng = null;

        resources.forEach(resource => {
            if (!resource.extraData) {
                return;
            }
            resource.extraData.forEach(extraData => {
                if (extraData.name === 'Lat') {
                    lat = extraData.value;
                }
                if (extraData.name === 'Lon') {
                    lng = extraData.value;
                }
            })

            this.resourceModelStore.set(resource.identity, resource);
            toFilter.set(resource.identity, resource);
            resource.location = { lat: parseFloat(resource.extraData[4].value), lng: parseFloat(resource.extraData[5].value) };
        });

        let filteredMarkers: Map<string, CymbiotMarker> = this.filterResources(toFilter, this.mapElementsModelStore, this.mapLayers, this.resourceGroupsModelStore, this.mapFilterOptions, this.mapSearchProperties);

        filteredMarkers.forEach((marker, key) => {
            this.markersDictionary.set(key, marker);
        });

        this.cdRef.detectChanges();
    }

    updateOrCreateEvents(events: Event[]): void {
        events.forEach(event => {
            event.dataArray.forEach(data => {
                let length = data.resourceUri.length;
                let reducer = data.resourceUri[length - 1];

                const marker = this.markersDictionary.get(reducer.id);

                if (marker) {
                    if (marker.component) {
                        marker.component.data.addEvent = event;
                    }
                    this.addEventResourceModelStore.set(reducer.id, event);
                }
            });
        });
    }


    deleteEvents(events: Event[]): void {
        events.forEach(event => {
            event.dataArray.forEach(data => {
                let length = data.resourceUri.length;
                let reducer = data.resourceUri[length - 1];
                if (!this.markersDictionary[reducer.id]) {
                    return;
                }
                if (this.markersDictionary[reducer.id].component) {
                    this.markersDictionary[reducer.id].component.data.addEvent = null;
                }
                this.addEventResourceModelStore.delete(reducer.id);
            });
        });
    }

    deleteMarkers(resources: Resource[]): void {
        resources.forEach(resource => {
            this.resourceModelStore.delete(resource.identity);
            this.markersDictionary.delete(resource.identity);
        });
    }

    onMapExtentChanged(event: number[]): void {
        this.mapExtent = this.projectionTransform.fromExtentToObj(event);
        this.extentChanged.next(this.mapExtent);
        this.cymbiotPopupService.clearClusters(this.instanceId);
        this.cymbiotPopupService.registerCluster(this.instanceId, this.markersArray, this.currentIndex)
    }

    getMapInfo(mapId: Guid): void {
        let getMapSubscription = this.mapService.get(mapId).subscribe(res => {
            this.selectedMap = new MapModel(res);
            this.resourceModelStore.clear();
            this.mapElementsModelStore.clear();
            switch (this.selectedMap.type) {
                case MapType.RASTER: {
                    this.initRasterMap(mapId.toString());
                    break;
                }
                case MapType.GIS: {
                    this.initGisMap();
                    break;
                }
                default: {
                    this.initGisMap();
                    break;
                }
            }
            if (!this.defaultItemId && !this.zoom) {
                this.extent = this.cymbiotMapUtilsService.returnExtent(this.selectedMap);
                this.mapExtent = this.extent;
            }
        });
        this.subscriptions.shared.push(getMapSubscription);
    }

    getMapElements(mapId: string): Observable<Map<string, MapElement>> {
        return this.mapElementService.getAll({ filters: [{ mapIdentity: mapId }] }).pipe(
            map(modelStore => {
                const mapElements = new Map<string, MapElement>();
                Object.keys(modelStore).forEach(key => {
                    mapElements.set(key, modelStore[key]);
                });
                return mapElements;
            })
        );
    }

    getMapLayers(mapId: string): Observable<Map<string, MapLayer>> {
        return this.mapLayerService.getAll({ filters: [{ mapIdentity: mapId }] }).pipe(
            map(modelStore => {
                const mapLayers = new Map<string, MapLayer>();
                Object.keys(modelStore).forEach(key => {
                    mapLayers.set(key, modelStore[key]);
                });
                return mapLayers;
            })
        );
    }

    getResourceGroups(): Observable<Map<string, ResourceGroup>> {
        return this.resourceGroupService.getAll().pipe(
            map(modelStore => {
                const resourceGroups = new Map<string, ResourceGroup>();
                Object.keys(modelStore).forEach(key => {
                    resourceGroups.set(key, modelStore[key]);
                });
                return resourceGroups;
            })
        );
    }

    private applyFilters(
        resources: Map<string, Resource>,
        mapElements: Map<string, MapElement>,
        mapLayers: Map<string, MapLayer>,
        resourceGroups: Map<string, ResourceGroup>,
        mapFilterOptions: MapFilters,
        mapSearchProperties: MapSearchProperties
    ): void {
        let idsToRemove: Map<string, boolean> = new Map<string, boolean>();

        if (this.markersDictionary instanceof Map) {
            // Handle Map case
            this.markersDictionary.forEach((_, guid: string) => {
                idsToRemove.set(guid, false);
            });

            let filtered: Map<string, CymbiotMarker> = this.filterResources(resources, mapElements, mapLayers, resourceGroups, mapFilterOptions, mapSearchProperties);

            filtered.forEach((value, key) => {
                this.markersDictionary.set(key, value);
                idsToRemove.delete(key);
            });

            idsToRemove.forEach((value, key) => {
                this.markersDictionary.delete(key);
            });
        } else if (typeof this.markersDictionary === 'object') {
            // Handle plain object case
            Object.keys(this.markersDictionary).forEach(guid => {
                idsToRemove.set(guid, false);
            });

            let filtered: Map<string, CymbiotMarker> = this.filterResources(resources, mapElements, mapLayers, resourceGroups, mapFilterOptions, mapSearchProperties);

            filtered.forEach((value, key) => {
                (this.markersDictionary as any)[key] = value;
                idsToRemove.delete(key);
            });

            idsToRemove.forEach((value, key) => {
                delete (this.markersDictionary as any)[key];
            });
        } else {
            throw new Error('markersDictionary is neither an instance of Map nor a plain object');
        }
    }

    forceChangeDetectionOnChildComponents(): void {
        this.markersDictionary = {...this.markersDictionary};
        this.cdRef.detectChanges();
    }

    centerOnMarker(marker: CymbiotMarker, zoom: number = null): void {
        let center = this.projectionTransform.fromLatLng(marker.x, marker.y);
        this.map.setCenter(center, zoom, false);
    }

    public centerOnCoordinates(latitude: number, longitude: number): void {
        let center = this.projectionTransform.fromLatLng(latitude, longitude);
        this.map.setCenter(center, this.zoom);
    }

    private filterResources(
        resources: Map<string, Resource>,
        mapElements: Map<string, MapElement>,
        mapLayers: Map<string, MapLayer>,
        resourceGroups: Map<string, ResourceGroup>,
        mapFilterOptions: MapFilters,
        mapSearchProperties: MapSearchProperties
    ): Map<string, CymbiotMarker> {
        const mergedLayers = this.mergeLayersAndFilters(mapFilterOptions);
        const filteredResourceArray = this.getFilteredResources(resources, mergedLayers, mapSearchProperties);
        let fixedCoordinatesResources = this.mapService.FixCoordinatesOverlappingIntoMap(filteredResourceArray);

        const result = this.createMarkers(filteredResourceArray, fixedCoordinatesResources, resources);

        const filteredResourceGroups = this.getFilteredResourceGroups(resourceGroups, mergedLayers);
        this.processFilteredResourceGroups(filteredResourceGroups, result, resourceGroups);

        this.processMapElements(mapElements, result);

        this.cymbiotPopupService.registerCluster(this.instanceId, this.markersArray, this.currentIndex);
        return result;
    }

    private getFilteredResources(
        resources: Map<string, Resource>,
        mergedLayers: any,
        mapSearchProperties: MapSearchProperties
    ): Resource[] {
        const filteredResourceArray: Resource[] = [];
        resources.forEach((resource, key) => {
            const resourceBool = this.isResourceSelected(resource, mergedLayers);
            const searchBool = mapSearchProperties ? this.searchResource(resource, mapSearchProperties) : true;

            if (resourceBool && searchBool) {
                filteredResourceArray.push(resource);
            }
        });
        return filteredResourceArray;
    }

    private isResourceSelected(resource: Resource, mergedLayers: any): boolean {
        return (
            mergedLayers.selectedResources.includes(resource.identity) ||
            (mergedLayers.selectedResourceGroupsFilter.length === 0 && mergedLayers.selectedResources[0] === "ALL") ||
            (resource.groups && resource.groups.some(group => mergedLayers.selectedResourceGroupsFilter.includes(group.identity)))
        );
    }

    private createMarkers(
        filteredResourceArray: Resource[],
        overlappingResources: Map<string, Resource>,
        resources: Map<string, Resource>
    ): Map<string, CymbiotMarker> {
        const returnMarkers: Map<string, CymbiotMarker> = new Map<string, CymbiotMarker>();
        const resourceLatLngdictionary: Map<string, Resource> = new Map<string, Resource>();

        filteredResourceArray.forEach(resource => {
            if (!overlappingResources.has(resource.identity)) {
                let marker = this.markersDictionary[resource.identity];
                if (!marker) {
                    marker = this.createOrUpdateMarker(resource, resourceLatLngdictionary);
                } else {
                    this.updateExistingMarker(marker, resource);
                }
                if (!this.isDefault) {
                    returnMarkers.set(marker.id, marker);
                }
            }
        });

        this.processOverlappingMarkers(overlappingResources, returnMarkers, resources);
        this.processResourceLatLngDictionary(resourceLatLngdictionary, returnMarkers);

        return returnMarkers;
    }

    private createOrUpdateMarker(resource: Resource, resourceLatLngdictionary: Map<string, Resource>): CymbiotMarker {
        let marker: CymbiotMarker;
        if (this.isDefault) {
            const latLngKey = resource.location.lat + resource.location.lng;
            if (
                !resourceLatLngdictionary.has(latLngKey.toString()) &&
                resource.resourceType !== ServerTypes.Core_RES_Output &&
                resource.resourceType !== ServerTypes.Core_RES_Input
            ) {
                resourceLatLngdictionary.set(latLngKey.toString(), resource);
            }
        } else {
            marker = this.createMarker<CustomMarkerComponent>(resource, CustomMarkerComponent, this.selectedMap.identity);
        }
        return marker;
    }

    private updateExistingMarker(marker: CymbiotMarker, resource: Resource): void {
        marker.x = resource.location.lat;
        marker.y = resource.location.lng;
        if (marker.component) {
            marker.component.data = this.returnMarkerData(resource);
        }
    }

    private processOverlappingMarkers(
        overlappingResources: Map<string, Resource>,
        returnMarkers: Map<string, CymbiotMarker>,
        resources: Map<string, Resource>
    ): void {
        overlappingResources.forEach((value, key) => {
            const resource = resources.get(key);
            let marker = this.markersDictionary[resource.identity];
            if (marker) {
                this.deleteMarkers([resource]);
            }
            marker = this.createMarker<CustomMarkerComponent>(resource, CustomMarkerComponent);
            returnMarkers.set(marker.id, marker);
        });
    }

    private processResourceLatLngDictionary(
        resourceLatLngdictionary: Map<string, Resource>,
        returnMarkers: Map<string, CymbiotMarker>
    ): void {
        if (this.isDefault) {
            resourceLatLngdictionary.forEach(resource => {
                const marker = this.createMarker<CustomMarkerComponent>(resource, CustomMarkerComponent, this.selectedMap.identity);
                returnMarkers.set(marker.id, marker);
            });
        }
    }

    private getFilteredResourceGroups(
        resourceGroups: Map<string, ResourceGroup>,
        mergedLayers: any
    ): string[] {
        const filteredResourceGroups: string[] = [];
        resourceGroups.forEach((group, key) => {
            if (mergedLayers.selectedResourceGroups.includes(group.identity)) {
                filteredResourceGroups.push(key);
            }
        });
        return filteredResourceGroups;
    }

    private processFilteredResourceGroups(
        filteredResourceGroups: string[],
        returnMarkers: Map<string, CymbiotMarker>,
        resourceGroups: Map<string, ResourceGroup>
    ): void {
        filteredResourceGroups.forEach(key => {
            const group = resourceGroups.get(key);
            let marker = this.markersDictionary[group.identity];
            if (!marker) {
                marker = this.createMarker<ResourceGroupMarkerComponent>(group, ResourceGroupMarkerComponent, this.selectedMap.identity);
            } else {
                if (marker.component) {
                    marker.component.data = this.returnMarkerData(group);
                }
            }
            returnMarkers.set(marker.id, marker);
        });
    }

    private processMapElements(
        mapElements: Map<string, MapElement>,
        returnMarkers: Map<string, CymbiotMarker>
    ): void {
        mapElements.forEach((mapElement) => {
            if (mapElement instanceof TextMapElement) {
                let marker = this.markersDictionary[mapElement.identity];
                if (!marker) {
                    marker = this.createMarker(mapElement as TextMapElement, TextMarkerComponent, this.selectedMap.identity);
                } else {
                    if (marker.component) {
                        marker.component.data = this.returnMarkerData(mapElement as TextMapElement);
                    }
                }
                returnMarkers.set(marker.id, marker);
            }
        });
    }

    private mergeLayersAndFilters(mapFilterOptions: MapFilters): MapLayer {
        let layer = {
            identity: Guid.create().toString(),
            selectedResourceGroups: [],
            selectedResources: mapFilterOptions && mapFilterOptions.selectedResources && mapFilterOptions.selectedResources.length > 0 ? mapFilterOptions.selectedResources : [],
            selectedTypes: [],
            selectedStates: [],
            selectedResourceGroupsFilter: mapFilterOptions && mapFilterOptions.selectedResourceGroups && mapFilterOptions.selectedResourceGroups.length > 0 ? mapFilterOptions.selectedResourceGroups : []
        };

        for (let mapLayer of this.mapLayers.values())
        {
            if (!mapLayer.hidden) {
                for (let key in layer) {
                    layer[key] = _.union(layer[key], mapLayer[key]);
                }
            }
            else {
                for (let resource of mapLayer.selectedResources){
                    delete this.markersDictionary[resource];
                }
            }
        }
        return layer;
    }

    searchResource(resource: Resource, mapSearchProperties: MapSearchProperties): boolean {
        let filtered: boolean;
        let resourcePropertyArray = this.returnResourceProperty(resource, mapSearchProperties.fieldName);

        let searchText = mapSearchProperties.freeText ? mapSearchProperties.freeText.toLowerCase() : null;

        switch (mapSearchProperties.operand) {
            case MapSearchOperand.contains:
                filtered = resourcePropertyArray.some(el => { return el.includes(searchText); });
                break;
            case MapSearchOperand.notContains:
                filtered = resourcePropertyArray.some(el => { return !el.includes(searchText); });
                break;
            case MapSearchOperand.equals:
                filtered = resourcePropertyArray.some(el => { return el === searchText; });
                break;
            case MapSearchOperand.notEquals:
                filtered = resourcePropertyArray.some(el => { return el !== searchText; });
                break;
            default:
                filtered = true;
                break;
        }


        return filtered;
    }

    returnResourceProperty(resource: Resource, resourceProperty: string): string[] {
        let resourcePropertyArray: string[] = [];

        switch (resourceProperty) {
            case 'name':
            case 'status':
                resourcePropertyArray.push(resource[resourceProperty].toString().toLowerCase());
                break;
            case 'resourceGroup':
                resource.groups.forEach(el => { return resourcePropertyArray.push(el.name.toLowerCase()); });
                break;
            case 'extraData':
                resource.extraData.forEach(el => { return resourcePropertyArray.push(el.value.toLowerCase()); });
                break;
            default:
                resourcePropertyArray.push(resource.name.toLowerCase());
                break;
        }
        return resourcePropertyArray;
    }

    activateZoomToFit(): void {
        if (!this.zoom)
        {
            this.zoomToFitEnabled = true;
            setTimeout(() => {
                this.zoomToFitEnabled = false;
            }, 10);
        }
    }

    setZoomFromRadius(radius: number): void {
        let calculatedZoom = this.mapService.convertRadiusToZoomLevel(radius);
        this.zoom = calculatedZoom;
    }

    updateSize(): void {
        this.map.updateSize();
    }

    initGisMap(): void {
        this.subscriptions.rasterMap.forEach(subscription => {
            subscription.unsubscribe();
            subscription.remove;
        });

        this.mapTileSource = this.cymbiotMapUtilsService.returnTileLayerSource(GisTileProvider.OSM);

        let signalRSubscription = this.signalRService.GetConnection('MapHub').subscribe(res => {
            this.signalRConnection = res;
            this.signalRConnection.invoke('PageLoaded', this.authService.user.Identity, this.selectedMapId);
        });
        this.subscriptions.gisMap.push(signalRSubscription);

        let resourcesSubscription = this.resourceCacheService.storageCompleted.subscribe(() => {
            this.resources = this.resourceCacheService.getAll();

            this.mapLayersObservable = this.getMapLayers(this.selectedMapId);
            this.resourceGroupsObservable = this.getResourceGroups();

            let resourceAndMapLayersSubscription = combineLatest([this.mapLayersObservable, this.resourceGroupsObservable])
                .subscribe(res => {
                    this.handleMapLayersAndResources(res);
                });
            this.subscriptions.gisMap.push(resourceAndMapLayersSubscription);

            let dataChangeResourceSubscription = this.resourceService.resourceChanged.subscribe(res => {
                switch (res.type) {
                    case DataChangeType.Update:
                        this.updateOrCreateMarkers(res.models);
                        break;
                    case DataChangeType.Create:
                        this.updateOrCreateMarkers(res.models);
                        break;
                    case DataChangeType.Delete:
                        this.deleteMarkers(res.models);
                        break;
                    default:
                        break;
                }
            });
            this.subscriptions.gisMap.push(dataChangeResourceSubscription);
        });
        this.subscriptions.gisMap.push(resourcesSubscription);
    }

    private mergeLayersVisibility(originalMapLayers: MapLayer[]): Map<string, MapLayer> {

        const mergedMapLayers = new Map<string, MapLayer>();

        this.mapLayers.forEach((mapLayer: MapLayer) => {
            mergedMapLayers.set(mapLayer.identity, mapLayer);
        });

        originalMapLayers.forEach((mapLayer: MapLayer) => {
            const modifiedMapLayer = mergedMapLayers.get(mapLayer.identity);
            if (modifiedMapLayer) {
                mapLayer.hidden = modifiedMapLayer.hidden;
            }
            else {
                mergedMapLayers.set(mapLayer.identity, mapLayer);
            }
        });

        return mergedMapLayers;
    }

    private handleMapLayersAndResources(res: any[]): void {

        this.mapLayers = this.mergeLayersVisibility(res[0]);

        this.resources.forEach(resource => {
            let canBeDisplayed = (resource.location && resource.location.lat && resource.location.lng);

            if (!canBeDisplayed) {
                return;
            }
            this.resourceModelStore.set(resource.identity, resource);
        });

        res[1].forEach((item: ResourceGroup) => {
            if (item.isHomogeneous && item.location && item.location.lat && item.location.lng) {
                this.resourceGroupsModelStore.set(item.identity, item);
            }
        });

        this.applyFilters(this.resourceModelStore, this.mapElementsModelStore, this.mapLayers, this.resourceGroupsModelStore, this.mapFilterOptions, this.mapSearchProperties);

        this.forceChangeDetectionOnChildComponents();

        // Handle zoom to entity for GIS maps (same logic as in initRasterMap)
        if (this.defaultItemId) {
            let marker: CymbiotMarker;

            if (this.markersDictionary instanceof Map) {
                marker = this.markersDictionary.get(this.defaultItemId);
            } else if (typeof this.markersDictionary === 'object') {
                marker = (this.markersDictionary as any)[this.defaultItemId];
            } else {
                throw new Error('markersDictionary is neither an instance of Map nor a plain object');
            }

            if (marker) {
                this.activateMarker(marker.component, marker, 30);
            }
        } else if (this.defaultLatitude && this.defaultLongitude) {
            this.centerOnCoordinates(this.defaultLatitude, this.defaultLongitude);
        } else {
            this.currentIndex = null;
        }

        let statusesSubscription = this.resourceService.getAllStatuses().subscribe(statuses => {
            let resourcesToUpdate: Resource[] = [];
            statuses.forEach(status => {
                let idAsString = status.Id.toString();
                let resource = this.resourceModelStore.get(idAsString);
                if (!resource) {
                    return;
                }
                let changed: boolean = (resource.status !== status.State || !resource.strength || (resource.strength !== status.Strength));
                if (!changed) {
                    return;
                }
                resource.status = status.State;
                resource.strength=status.Strength ;
                this.resourceService.update(resource);
                resourcesToUpdate.push(resource);
            });

            this.updateOrCreateMarkers(resourcesToUpdate);

            if (this.defaultItemId) {
                let marker: CymbiotMarker;

                if (this.markersDictionary instanceof Map) {
                    marker = this.markersDictionary.get(this.defaultItemId);
                } else if (typeof this.markersDictionary === 'object') {
                    marker = (this.markersDictionary as any)[this.defaultItemId];
                } else {
                    throw new Error('markersDictionary is neither an instance of Map nor a plain object');
                }

                if (marker) {
                    this.activateMarker(marker.component, marker, 30);
                }
            } else if (this.defaultLatitude && this.defaultLongitude) {
                this.centerOnCoordinates(this.defaultLatitude, this.defaultLongitude);
            } else {
                this.currentIndex = null;
            }

            this.cymbiotPopupService.clearClusters(this.instanceId);
            this.cymbiotPopupService.registerCluster(this.instanceId, this.markersArray, this.currentIndex);
        });

        this.subscriptions.shared.push(statusesSubscription);
    }

    initRasterMap(mapId: string): void {
        this.subscriptions.gisMap.forEach(subscription => {
            subscription.unsubscribe();
            subscription.remove;
        });

        let rasterImageSubscription = this.mapService.getRasterImage(mapId).subscribe(res => {
            this.mapTileSource = new Base64TileSource({ base64Image: res, projection: { code: ProjectionEnum.IMAGE, units: ProjectionUnits.PIXELS } });
        });
        this.subscriptions.rasterMap.push(rasterImageSubscription);

        let mapElementsSubscription = this.getMapElements(mapId).subscribe(res => {
            this.resourceModelStore.clear();
            for (let key in res) {
                switch (res[key].getType()) {
                    case MapElementType.Text: {
                        this.mapElementsModelStore.set(key, res[key]);
                        break;
                    }
                    default: {
                        this.resourceModelStore.set(key, res[key]['resource']);
                        break;
                    }
                }
            }
            this.applyFilters(this.resourceModelStore, this.mapElementsModelStore, this.mapLayers, this.resourceGroupsModelStore, this.mapFilterOptions, this.mapSearchProperties);
        });
        this.subscriptions.rasterMap.push(mapElementsSubscription);
    }

   private deferShowPopup(marker: CymbiotMarker) {
        let subscription = this.map.renderCompleted.subscribe((flag) => {
            this.showPopup(marker);
            subscription.unsubscribe();
        });
    }

    private showPopup(marker: CymbiotMarker) {
        marker.generateTemplateRef([]);
        marker.component.showPopUp();
        this.openedMarkerComponent = marker.component;
    }

    private createMarker<T extends DefaultMarkerComponent>(element: Resource | TextMapElement | ResourceGroup, markerComponent: Type<T>, mapId?: string): CymbiotMarker {
        let marker = new CymbiotMarker({
            x: element.location.lat,
            y: element.location.lng
        });

        this.markersArray.push(marker);
        marker.id = element.identity;
        marker.generateTemplateRef = (markers: Marker[]) => {
            if (!marker.component) {
                let factory = this.componentFactoryResolver.resolveComponentFactory(markerComponent as Type<T>);
                let componentRef: ComponentRef<DefaultMarkerComponent> = this.customMarkersFactory.createComponent(factory);
                componentRef.instance.data = this.returnMarkerData(element);
                componentRef.instance.instanceId = mapId ? mapId : this.instanceId;

                let markerClickSubscription = componentRef.instance['onClick'].subscribe(() => {
                    this.activateMarker(componentRef.instance, marker)
                });
                this.subscriptions.shared.push(markerClickSubscription);

                let removePopupSubscription = componentRef.instance['removePopUp'].subscribe(() => {
                    if (this.openedMarkerComponent) {
                        this.openedMarkerComponent.hidePopUp();
                    }
                    this.openedMarkerComponent = null;
                });
                this.subscriptions.shared.push(removePopupSubscription);

                componentRef.changeDetectorRef.detectChanges();

                marker.component = componentRef.instance;
            }

            return marker.component.getElementRef();
        };

        return marker;
    }

    public activateMarker(componentRef: DefaultMarkerComponent, marker: CymbiotMarker, zoom: number = null): void {
        marker.generateTemplateRef([]);

        if (this.openedMarkerComponent) {
            this.openedMarkerComponent.hidePopUp();
        }
        this.openedMarkerComponent = marker.component;

        this.currentIndex = this.markersArray.indexOf(marker);
        let center = this.projectionTransform.fromLatLng(marker.x, marker.y);
        this.openedMarkerComponent.showPopUp();
        let popupHeight = 400;
        let resolution = this.map.getZoom() ? Math.pow(2, 19 - this.map.getZoom()) : 1;
        let yOffset = (popupHeight * -0.1) * resolution;
        let adjustedCenter = {
            x: center.x,
            y: center.y - yOffset // Subtract to move the map up (OpenLayers coordinate system)
        };
        this.map.setCenter(adjustedCenter, zoom, true);
    }

    private returnMarkerData(element: Resource | TextMapElement | ResourceGroup): ResourceData | TextMapElement | ResourceGroupData {
        switch (true) {
            case element instanceof Resource:
                return {
                    resource: element as Resource,
                    addEvent: this.addEventResourceModelStore.get(element.identity) || null,
                    instanceId: this.instanceId
                };
            case element instanceof ResourceGroup:
                return {
                    resourceGroup: element as ResourceGroup,
                    addEvent: this.addEventResourceModelStore.get(element.identity) || null,
                    instanceId: this.instanceId
                };
            case element instanceof TextMapElement:
                return element as TextMapElement;
            default:
                break;
        }
    }
}

