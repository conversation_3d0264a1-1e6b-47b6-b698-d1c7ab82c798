/* You can add global styles to this file, and also import other style files */
@import "utils";
@import "rtl";
@import "responsive";
@import "appCustom";
@import "colors";
// @import "primeng";
@import "cymbiot";
@import "animate";
@import "sweetAlert";
@import "general/_primengOverrides";
@import "general/toastApp";
@import "skins/theme-color-classes";
@import "general/primengComponents.scss";
@import 'skins/skins';
@import 'simple-dark-mode';

/* Global styles for viewport handling */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden !important; /* Force no overflow */
}

body {
  position: relative;
  font-family: "Rounded Mplus 1c Bold", ui-sans-serif, system-ui, sans-serif;
  overflow: hidden !important; /* Ensure no body scroll */
}

// Iy's use only when app is built from CC
body.menuless-app {

    #masterHeader,
    .main-sidebar {
        display: none;
    }

    #results-div {
        margin-top: 0px;
        height: 100vh;
        min-height: 100vh;
        max-height: 100vh;
    }

    .main-container {
        display: block;
        margin-left: auto;
        margin-right: auto;
        -ms-overflow-x: hidden;
        overflow-x: hidden;
        overflow-y: scroll;
        position: relative;
        overflow: hidden;
        height: 100vh;
    }
}

.sweetalert-lg {
    width: 800px !important;
}

.modal-content{
    background: linear-gradient(#962DFF, #393197);

    &.max-height{
        height: 90%;
    }

    &.min-width{
        width: 15%;
    }

    .p-dialog-header, .p-dialog-content, .p-dialog-footer{
        background: transparent;
        border: none;

        .p-dialog-title{
            color: #F2F6F3;
        }

        .p-dialog-header-icon{
            color: #F2F6F3;
        }

        .p-dialog-header-icon:enabled:hover{
            color: #F2F6F3;
        }
    }

    .field{
        background: rgba(66, 49, 161, 0.25);
        color: #F2F6F3;
        border: 1px solid rgba(255, 255, 255, 0.25);

    }

    .field::placeholder{
        color: #F2F6F3;
        opacity: 1;
    }
}

.modal-partial {
    word-wrap: break-word;
}

.modal-label {
    font-weight: 500;
    font-size: 1rem;
    color: #222222;
}

.border {
    border: 1px solid #E8E8E8;
}

.center-content {
    text-align: center;
    align-items: center;
    justify-content: center;
    display: flex;
    white-space: normal !important;
    padding: 5px;
}

.border-bottom {
    border-bottom: 1px solid var(--secondary-highlight-5);
}

form {
    overflow: visible;
}

.p-datatable .p-datatable-header,
.p-multiselect-panel .p-multiselect-header {
    background: #ffffff;
}

.p-datatable.p-datatable-scrollable>.p-datatable-wrapper>.p-datatable-table>.p-datatable-thead,
.p-datatable.p-datatable-scrollable>.p-datatable-wrapper>.p-datatable-table>.p-datatable-tfoot {
    min-width: 1400px;
}

.p-datatable-responsive-scroll>.p-datatable-wrapper>table,
.p-datatable-auto-layout>.p-datatable-wrapper>table {
    font-size: 13px;
}

.p-checkbox .p-checkbox-box {
    width: 14px;
    height: 14px;
}

.p-dropdown,
.p-multiselect {
    width: 100%;
}

body .ui-calendar .ui-calendar-button {
    height: 46px;
}

.p-overlaypanel .p-overlaypanel-content {
    padding: 0px;
}

.p-component,
.p-component * {
    font-size: 14px;
}

.p-dataview .p-dataview-header {
    background-color: #fff;
}
.p-dataview-content{
    overflow: scroll;

    height: 600px;
}
.p-selectbutton .p-button,
.p-selectbutton .p-button.p-highlight {
    background: none;
    color: #003cbf;
    border: none;
    font-size: 14px;
}

.pi-bars,
.pi-th-large {
    font-size: 28px !important;
}

.p-toast-bottom-right {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
}

.p-toast-detail {
    white-space: pre-wrap;
}

.actions-menu,
.p-overlaypanel-content ul {
    list-style-type: none;
    padding: 0px;
}

.actions-menu li,
.p-overlaypanel-content ul li {
    padding: 10px;
}

.menu-items-wrapper{
    margin-top: 0.5em;
}

.content-container{
    width: 100% !important;
}